#!/bin/bash

echo "🚀 Installing YOLOv8 Dependencies..."
echo "===================================="

# Activate virtual environment if it exists
if [ -d "venv" ]; then
    echo "📦 Activating virtual environment..."
    source venv/bin/activate
fi

# Install ultralytics and dependencies
echo "📥 Installing ultralytics package..."
pip install ultralytics torch torchvision --upgrade

# Download YOLOv8 model
echo "📦 Pre-downloading YOLOv8 model..."
python3 << EOF
from ultralytics import YOLO
print("Downloading YOLOv8n model...")
model = YOLO('yolov8n.pt')
print("✅ Model downloaded successfully!")
print(f"Model location: ~/.cache/ultralytics/yolov8n.pt")
EOF

echo ""
echo "✅ YOLOv8 installation complete!"
echo "===================================="
echo "You can now use YOLOv8 for object detection"
echo ""
