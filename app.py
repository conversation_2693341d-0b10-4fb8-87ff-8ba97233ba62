import streamlit as st
import boto3
import json
import os
import time
import subprocess
from datetime import datetime
import numpy as np
import yt_dlp
from dotenv import load_dotenv
import chromadb
import hashlib
import pickle
import base64
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go

# Configure Streamlit page
st.set_page_config(
    page_title="Video Search App",
    page_icon="🎥",
    layout="wide",
    initial_sidebar_state="expanded"
)
# Optional imports for video processing features
try:
    import cv2
    from tqdm import tqdm
    VIDEO_PROCESSING_AVAILABLE = True
except ImportError as e:
    print(f"⚠️  Video processing features disabled: {e}")
    VIDEO_PROCESSING_AVAILABLE = False

load_dotenv()

# Config (set in .env or secrets)
S3_BUCKET = os.getenv('S3_BUCKET', 'poc-video-search-bucket')
BEDROCK_REGION = os.getenv('BEDROCK_REGION', 'us-east-1')
CHROMA_COLLECTION = 'video_embeddings'
AWS_ACCESS_KEY_ID = os.getenv('AWS_ACCESS_KEY_ID')
AWS_SECRET_ACCESS_KEY = os.getenv('AWS_SECRET_ACCESS_KEY')
AWS_ACCOUNT_ID = os.getenv('AWS_ACCOUNT_ID')

# Initialize AWS clients
try:
    # AWS Identity check
    sts = boto3.client('sts', region_name=BEDROCK_REGION)
    try:
        identity = sts.get_caller_identity()
        account_id = identity.get('Account', 'Unknown')
        arn = identity.get('Arn', 'Unknown')
        print("✅ AWS Identity verified:", arn)
        print(f"✅ Account ID: {account_id}")
        
        print(f"✅ Using AWS account: {account_id}")
    except Exception as e:
        print(f"⚠️  AWS Identity check failed: {e}")
        print("Continuing with app initialization...")

    # S3 client
    if AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY:
        s3 = boto3.client('s3', region_name=BEDROCK_REGION, aws_access_key_id=AWS_ACCESS_KEY_ID, aws_secret_access_key=AWS_SECRET_ACCESS_KEY)
        print(f"✅ S3 client initialized with explicit credentials: {AWS_ACCESS_KEY_ID[:10]}...")
    else:
        s3 = boto3.client('s3', region_name=BEDROCK_REGION)
        print("⚠️  S3 client initialized with default credentials")

    # Bedrock client
    if AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY:
        bedrock = boto3.client('bedrock-runtime', region_name=BEDROCK_REGION, aws_access_key_id=AWS_ACCESS_KEY_ID, aws_secret_access_key=AWS_SECRET_ACCESS_KEY)
        print(f"✅ Bedrock client initialized with explicit credentials: {AWS_ACCESS_KEY_ID[:10]}...")
    else:
        bedrock = boto3.client('bedrock-runtime', region_name=BEDROCK_REGION)
        print("⚠️  Bedrock client initialized with default credentials")
    
    # Rekognition client
    if AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY:
        rekognition = boto3.client('rekognition', region_name=BEDROCK_REGION, aws_access_key_id=AWS_ACCESS_KEY_ID, aws_secret_access_key=AWS_SECRET_ACCESS_KEY)
        print(f"✅ Rekognition client initialized with explicit credentials: {AWS_ACCESS_KEY_ID[:10]}...")
    else:
        rekognition = boto3.client('rekognition', region_name=BEDROCK_REGION)
        print("⚠️  Rekognition client initialized with default credentials")

    print("✅ AWS clients initialized successfully")
except Exception as e:
    print(f"❌ Error initializing AWS clients: {e}")
    st.error(f"Failed to initialize AWS services: {e}")
    st.stop()

# Global declaration for chroma_collection
global chroma_collection

# Initialize ChromaDB
try:
    # Create ChromaDB client with persistent storage
    chroma_client = chromadb.PersistentClient(path="./chroma_db")
    
    # Get or create collection
    try:
        chroma_collection = chroma_client.get_collection(name=CHROMA_COLLECTION)
        print("✅ ChromaDB collection already exists")
    except:
        chroma_collection = chroma_client.create_collection(
            name=CHROMA_COLLECTION,
            metadata={"hnsw:space": "cosine"}
        )
        print("✅ ChromaDB collection created successfully")
    
    print("✅ ChromaDB initialized successfully")
except Exception as e:
    print(f"❌ Error initializing ChromaDB: {e}")
    st.error(f"Failed to initialize ChromaDB: {e}")
    st.stop()

# Simple authentication
def check_password():
    """Returns `True` if the user had the correct password."""
    
    def password_entered():
        """Checks whether a password entered by the user is correct."""
        password_value = st.session_state.get("password", "")
        if password_value == "minfy2025":
            st.session_state["password_correct"] = True
            if "password" in st.session_state:
                del st.session_state["password"]  # don't store password
        elif password_value:  # Only set to False if a password was actually entered
            st.session_state["password_correct"] = False

    # Return True if the password is validated initially.
    if st.session_state.get("password_correct", False):
        return True

    # Show input for password.
    st.text_input(
        "Enter password to access the app", type="password", on_change=password_entered, key="password"
    )
    # Only show error if password was explicitly marked as incorrect (not just missing)
    if st.session_state.get("password_correct") == False:
        st.error("😕 Password incorrect")
    return False

if not check_password():
    st.stop()  # Do not continue if check_password is not True.

st.title('🎥 Video Search App')

# Model selection UI at the top
st.subheader('🤖 Embedding Model Selection')
col1, col2 = st.columns([1, 2])
with col1:
    # Use session state to persist selection
    if 'embedding_model' not in st.session_state:
        st.session_state['embedding_model'] = 'AWS Nova Premier'
    
    embedding_model = st.selectbox(
        'Choose Embedding Model:',
        ['AWS Nova Premier', 'TwelveLabs Marengo'],
        index=['AWS Nova Premier', 'TwelveLabs Marengo'].index(st.session_state['embedding_model']),
        help='Select the embedding model for video processing and search',
        key='embedding_model_selector'
    )
with col2:
    if embedding_model == 'AWS Nova Premier':
        st.info('🟣 **AWS Nova Premier**: State-of-the-art multimodal embeddings with native video understanding and 1M context window (Recommended)')
    else:
        st.info('🔶 **TwelveLabs Marengo**: Optimized for video embeddings with excellent semantic search performance')

# DEBUG: Log model selection changes
print(f"🔍 DEBUG: Model selection UI - embedding_model='{embedding_model}'")
print(f"🔍 DEBUG: Model selection UI - previous session_state='{st.session_state.get('embedding_model', 'NOT_SET')}'")

# Store model selection in session state
st.session_state['embedding_model'] = embedding_model

# DEBUG: Verify session state was updated
print(f"🔍 DEBUG: Model selection UI - updated session_state='{st.session_state.get('embedding_model', 'NOT_SET')}'")

if "indexing_jobs" not in st.session_state:
    st.session_state["indexing_jobs"] = {}

# Session state for UI messages
if "upload_messages" not in st.session_state:
    st.session_state["upload_messages"] = []
if "indexing_messages" not in st.session_state:
    st.session_state["indexing_messages"] = []

# Session state for tab management
if "active_tab" not in st.session_state:
    st.session_state["active_tab"] = 0

def to_ffmpeg_ts(seconds):
    """Convert seconds to HH:MM:SS.mmm format for FFmpeg"""
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    secs = seconds % 60
    return f"{hours:02d}:{minutes:02d}:{secs:06.3f}"

def generate_video_id(filename):
    """Generate a unique video ID from filename"""
    import re
    base = os.path.basename(filename)
    # Remove file extension
    name_without_ext = os.path.splitext(base)[0]
    # Remove special characters and spaces, convert to lowercase
    cleaned = re.sub(r'[^a-zA-Z0-9]', '', name_without_ext).lower()
    # If too short, pad with hash of original name
    if len(cleaned) < 8:
        import hashlib
        hash_suffix = hashlib.md5(base.encode()).hexdigest()[:4]
        cleaned = cleaned + hash_suffix
    # Take first 8 characters and last 4 for uniqueness
    if len(cleaned) <= 12:
        return cleaned
    return cleaned[:8] + cleaned[-4:]

def generate_nova_embeddings_for_video(video_id, s3_key):
    """Generate embeddings using TwelveLabs Marengo async API"""
    try:
        print(f"🔄 Starting Marengo embedding generation for {video_id}")
        print(f"⚠️ Note: Using TwelveLabs Marengo for embeddings (Nova Multimodal Embeddings doesn't support video S3 URIs)")
        
        s3_uri = f"s3://{S3_BUCKET}/{s3_key}"
        
        model_input = {
            "inputType": "video",
            "mediaSource": {
                "s3Location": {
                    "uri": s3_uri,
                    "bucketOwner": AWS_ACCOUNT_ID
                }
            }
        }
        
        # Output configuration for async job
        output_config = {
            "s3OutputDataConfig": {
                "s3Uri": f"s3://{S3_BUCKET}/embeddings-output/{video_id}/"
            }
        }
        
        # Ensure AWS_ACCOUNT_ID is string
        if not isinstance(AWS_ACCOUNT_ID, str):
            raise ValueError(f"AWS_ACCOUNT_ID must be string, got {type(AWS_ACCOUNT_ID)}")
        
        print(f"📤 Calling Marengo async API with start_async_invoke...")
        print(f"   Model: twelvelabs.marengo-embed-2-7-v1:0")
        print(f"   Input: {s3_uri}")
        print(f"   Output: s3://{S3_BUCKET}/embeddings-output/{video_id}/")
        
        # Use start_async_invoke for Marengo
        response = bedrock.start_async_invoke(
            modelId='twelvelabs.marengo-embed-2-7-v1:0',
            modelInput=model_input,
            outputDataConfig=output_config
        )
        
        invocation_arn = response['invocationArn']
        print(f"✅ Marengo async job started successfully!")
        print(f"   Invocation ARN: {invocation_arn}")
        print(f"   Video ID: {video_id}")
        print(f"   Expected output: s3://{S3_BUCKET}/embeddings-output/{video_id}/output.json")
        print(f"   Processing time: ~2-5 minutes")
        print(f"   Check status in 'Uploaded Videos' tab")
        
        # Save job info with nova-premier model type
        # Note: We use Marengo for embeddings but label as nova-premier for UI
        output_key = f'embeddings-output/{video_id}/job_info.json'
        job_data = {
            'video_id': video_id,
            'invocation_arn': invocation_arn,
            'model': 'nova-premier',  # Label as nova-premier (uses Marengo for embeddings)
            'embedding_model': 'marengo',  # Actual embedding model used
            'timestamp': str(datetime.now())
        }
        upload_json_to_s3(job_data, output_key)
        
        return True
            
    except Exception as e:
        print(f"❌ Error generating embeddings: {e}")
        import traceback
        traceback.print_exc()
        return False

def get_video_summary(video_id):
    """Fetch video summary from S3 if available"""
    try:
        summary_key = f'summaries/{video_id}/safety_report.json'
        if s3_object_exists(summary_key):
            summary_data = download_json_from_s3(summary_key)
            return summary_data
        return None
    except Exception as e:
        print(f"⚠️ Error fetching summary for {video_id}: {e}")
        return None

def generate_nova_video_summary(video_id, s3_key):
    """Generate safety summary using Nova Premier"""
    try:
        print(f"🔄 Starting Nova Premier video analysis for {video_id}")
        
        # Use inference profile for Nova Premier (required)
        model_id = 'us.amazon.nova-premier-v1:0'
        
        # Create Bedrock client with longer timeout for video processing
        from botocore.config import Config
        bedrock_video = boto3.client(
            'bedrock-runtime',
            region_name=BEDROCK_REGION,
            aws_access_key_id=AWS_ACCESS_KEY_ID,
            aws_secret_access_key=AWS_SECRET_ACCESS_KEY,
            config=Config(
                read_timeout=300,  # 5 minutes for long videos
                connect_timeout=60,
                retries={'max_attempts': 2}
            )
        )
        
        request_body = {
            "schemaVersion": "messages-v1",
            "messages": [{
                "role": "user",
                "content": [
                    {
                        "video": {
                            "format": "mp4",
                            "source": {
                                "s3Location": {
                                    "uri": f"s3://{S3_BUCKET}/{s3_key}",
                                    "bucketOwner": AWS_ACCOUNT_ID
                                }
                            }
                        }
                    },
                    {
                        "text": """Analyze this dashcam video for fleet safety. Provide a structured report:

**EXECUTIVE SUMMARY**
Brief overview of the video content and overall safety assessment.

**SAFETY INCIDENTS** (with timestamps MM:SS)
- List each incident with timestamp
- Describe what happened
- Severity level (Low/Medium/High)

**RISK SCORE**: X/10
Overall risk assessment with justification.

**DETECTED BEHAVIORS**
- Driver behaviors observed
- Vehicle interactions
- Environmental factors

**OBJECTS DETECTED**
- Vehicles (cars, trucks, motorcycles)
- Pedestrians
- Traffic signs/signals
- Road conditions

**RECOMMENDATIONS**
1. Immediate actions needed
2. Driver coaching points
3. Policy considerations

Focus on: harsh braking, lane violations, distracted driving, aggressive driving, 
pedestrian interactions, traffic violations, unsafe following distance, speeding."""
                    }
                ]
            }],
            "system": [{
                "text": "You are an expert fleet safety analyst. Provide structured, actionable safety reports with specific timestamps."
            }],
            "inferenceConfig": {
                "maxTokens": 2000,
                "temperature": 0.3,
                "topP": 0.9
            }
        }
        
        print(f"📤 Calling Nova Premier for video analysis...")
        print(f"⏱️ Using 5-minute timeout for long video processing...")
        
        response = bedrock_video.invoke_model(
            body=json.dumps(request_body),
            modelId=model_id,
            accept='application/json',
            contentType='application/json'
        )
        
        response_body = json.loads(response.get('body').read())
        
        if 'output' in response_body and 'message' in response_body['output']:
            summary = response_body['output']['message']['content'][0]['text']
            
            # Save summary to S3
            summary_key = f'summaries/{video_id}/safety_report.json'
            summary_data = {
                'video_id': video_id,
                'summary': summary,
                'model': 'nova-premier',
                'timestamp': str(datetime.now())
            }
            upload_json_to_s3(summary_data, summary_key)
            
            print(f"✅ Nova Premier summary generated and saved: {summary_key}")
            return summary
        else:
            print(f"❌ No summary in Nova response: {response_body}")
            return None
            
    except Exception as e:
        print(f"❌ Error generating Nova summary: {e}")
        import traceback
        traceback.print_exc()
        return None

def generate_nova_text_embedding(text):
    """Generate text embedding using TwelveLabs Marengo"""
    try:
        print(f"⚠️ Using TwelveLabs Marengo for text embeddings (Nova fallback)")
        
        model_id = 'us.twelvelabs.marengo-embed-2-7-v1:0'
        body = json.dumps({
            'inputType': 'text',
            'inputText': text
        })
        
        response = bedrock.invoke_model(
            modelId=model_id,
            body=body,
            contentType='application/json',
            accept='application/json'
        )
        
        query_embed = json.loads(response['body'].read())['data'][0]['embedding']
        return query_embed
            
    except Exception as e:
        print(f"❌ Error generating text embedding: {e}")
        return None

def index_video(video_id, s3_key, model_type=None):
    """Start indexing for a video using the selected model"""
    try:
        # Use session state model if not specified
        if model_type is None:
            model_type = st.session_state.get('embedding_model', 'TwelveLabs Marengo')
        
        # DEBUG: Log the model selection
        print(f"🔍 DEBUG: index_video called with model_type='{model_type}'")
        print(f"🔍 DEBUG: session_state embedding_model='{st.session_state.get('embedding_model', 'NOT_SET')}'")
        
        # Check if video exists in S3
        try:
            s3.head_object(Bucket=S3_BUCKET, Key=s3_key)
        except Exception as e:
            st.error(f"Video not found in S3: {e}")
            return False
        
        if model_type == 'AWS Nova Premier':
            print(f"🔍 DEBUG: Using Nova Premier model path (with Marengo embeddings)")
            # Use Marengo for embeddings + Nova for summary
            print(f"🔄 Starting embeddings generation for {video_id}")
            st.session_state["indexing_jobs"][video_id] = "Indexing..."
            
            # Generate embeddings using Marengo (async)
            success = generate_nova_embeddings_for_video(video_id, s3_key)
            if success:
                # Generate Nova Premier summary (non-blocking - fire and forget)
                print(f"🔄 Starting Nova Premier video summary generation (async)...")
                try:
                    import threading
                    # Run summary generation in background thread
                    thread = threading.Thread(
                        target=generate_nova_video_summary,
                        args=(video_id, s3_key),
                        daemon=True
                    )
                    thread.start()
                    print(f"✅ Nova Premier summary generation started in background")
                except Exception as e:
                    print(f"⚠️ Failed to start summary thread (non-critical): {e}")
                
                print(f"✅ Embedding job started for {video_id}")
                return True
            else:
                st.session_state["indexing_jobs"][video_id] = "Failed: Embedding generation failed"
                return False
        else:
            print(f"🔍 DEBUG: Using Marengo model path (model_type='{model_type}')")
            # Use TwelveLabs Marengo (async processing)
            s3_uri = f"s3://{S3_BUCKET}/{s3_key}"
            
            model_input = {
                "inputType": "video",
                "mediaSource": {
                    "s3Location": {
                        "uri": s3_uri,
                        "bucketOwner": AWS_ACCOUNT_ID
                    }
                }
            }

            output_config = {
                "s3OutputDataConfig": {
                    "s3Uri": f"s3://{S3_BUCKET}/embeddings-output/{video_id}/",
                    "bucketOwner": AWS_ACCOUNT_ID
                }
            }
            
            print(f"DEBUG: model_input = {model_input}")
            print(f"DEBUG: output_config = {output_config}")
            print(f"DEBUG: AWS_ACCOUNT_ID = {AWS_ACCOUNT_ID} (type: {type(AWS_ACCOUNT_ID)})")
            
            response = bedrock.start_async_invoke(
                modelId='twelvelabs.marengo-embed-2-7-v1:0',
                modelInput=model_input,
                outputDataConfig=output_config
            )
            
            invocation_arn = response['invocationArn']
            print(f"✅ Bedrock async invocation started: {invocation_arn}")
            st.session_state["indexing_jobs"][video_id] = "Indexing..."
            return True
        
    except Exception as e:
        st.error(f"Indexing error: {str(e)}")
        return False

def check_and_ingest_embeddings(video_id, s3_key, expected_model_type=None):
    """Check and ingest embeddings from S3 to ChromaDB for the specified model"""
    prefixes_to_try = [
        f'embeddings-output/{video_id}/',
        f'embeddings-output/{video_id}.mp4/',  # Old format
    ]
    
    try:
        current_status = st.session_state["indexing_jobs"].get(video_id, "")
        if current_status != "Processing":
            st.session_state["indexing_jobs"][video_id] = "Waiting"

        # DEBUG: Log expected model type
        print(f"🔍 DEBUG: check_and_ingest_embeddings called with expected_model_type='{expected_model_type}'")

        # Check for Nova, Marengo embeddings
        output_keys = []
        nova_embeddings_key = None
        
        for prefix in prefixes_to_try:
            objects = s3.list_objects_v2(Bucket=S3_BUCKET, Prefix=prefix)
            for obj in objects.get('Contents', []):
                k = obj['Key']
                if k.startswith(prefix):
                    if k.endswith('output.json'):  # Marengo format
                        output_keys.append(k)
                    elif k.endswith('nova_embeddings.json'):  # Nova format
                        nova_embeddings_key = k
        
        # DEBUG: Log what embeddings were found
        print(f"🔍 DEBUG: Found {len(output_keys)} Marengo files, nova_embeddings_key={nova_embeddings_key is not None}")
        
        # Only process embeddings for the expected model type
        if expected_model_type == 'AWS Nova Premier':
            print(f"🔍 DEBUG: Nova Premier uses Marengo embeddings, checking for Marengo output")
            if not output_keys:
                print(f"❌ No Marengo embeddings found for {video_id}")
                st.session_state["indexing_jobs"][video_id] = "Waiting"
                return False
            # Process Marengo embeddings (continue with existing Marengo logic below)
        elif expected_model_type == 'TwelveLabs Marengo':
            print(f"🔍 DEBUG: Only processing Marengo embeddings")
            if not output_keys:
                print(f"❌ No Marengo embeddings found for {video_id}")
                st.session_state["indexing_jobs"][video_id] = "Failed: No Marengo embeddings found"
                return False
            # Process Marengo embeddings (continue with existing Marengo logic below)
        else:
            print(f"🔍 DEBUG: No specific model type, processing both (legacy behavior)")
            # Legacy behavior - process both if no specific model expected
        
        # Check if already ingested
        try:
            existing = chroma_collection.get(where={"video_id": video_id}, limit=1)
            if len(existing['ids']) > 0:
                st.session_state["indexing_jobs"][video_id] = "Completed"
                return True
        except Exception:
            pass

        # Process Nova embeddings if available (only if expected model is Nova or no specific model)
        if nova_embeddings_key and (expected_model_type == 'AWS Nova Premier' or expected_model_type is None):
            st.session_state["indexing_jobs"][video_id] = "Processing"
            try:
                obj = s3.get_object(Bucket=S3_BUCKET, Key=nova_embeddings_key)
                embeddings_data = json.loads(obj['Body'].read().decode('utf-8'))
                
                if 'embedding' in embeddings_data:
                    embedding = embeddings_data['embedding']
                    print(f"📖 Processing Nova Premier embedding (single video-level embedding)")
                    
                    # Nova Premier provides a single embedding for the entire video
                    vec = np.array(embedding)
                    if vec.size == 0:
                        print(f"❌ Empty Nova embedding vector")
                        return False
                    
                    norm = np.linalg.norm(vec)
                    if norm == 0:
                        print(f"❌ Zero-norm Nova embedding vector")
                        return False
                    
                    normalized_vector = (vec / norm).tolist()
                    
                    doc_id = f"{video_id}_nova_0"
                    
                    # Add to ChromaDB
                    chroma_collection.add(
                        ids=[doc_id],
                        embeddings=[normalized_vector],
                        metadatas=[{
                            'video_id': video_id,
                            'original_key': s3_key,
                            'segment_id': 0,
                            'timestamp': '00:00:00.000',
                            'raw_startSec': 0,
                            'upload_time': str(datetime.now()),
                            'model_type': 'nova'
                        }],
                        documents=[f"Nova Premier video embedding for {video_id}"]
                    )
                    
                    print(f"✅ ChromaDB ingestion successful for {video_id}: Nova Premier embedding")
                    st.session_state["indexing_jobs"][video_id] = "Completed"
                    
                    # Clear messages
                    st.session_state["upload_messages"] = [msg for msg in st.session_state["upload_messages"] if msg.get('video_id') != video_id]
                    st.session_state["indexing_messages"] = [msg for msg in st.session_state["indexing_messages"] if msg.get('video_id') != video_id]
                    
                    return True
                else:
                    print(f"❌ No 'embedding' field in Nova embeddings data")
                    return False
            except Exception as e:
                print(f"❌ Error processing Nova embeddings: {e}")
                import traceback
                traceback.print_exc()
                st.session_state["indexing_jobs"][video_id] = f"Failed: {str(e)}"
                return False
        
        # Process Marengo embeddings if available (for both Nova Premier and TwelveLabs Marengo)
        if output_keys and (expected_model_type in ['AWS Nova Premier', 'TwelveLabs Marengo'] or expected_model_type is None):
            st.session_state["indexing_jobs"][video_id] = "Processing"
            
            # Determine model_type for metadata
            # If Nova Premier selected, label as 'nova' even though using Marengo embeddings
            metadata_model_type = 'nova' if expected_model_type == 'AWS Nova Premier' else 'marengo'
            doc_prefix = 'nova' if expected_model_type == 'AWS Nova Premier' else 'marengo'
            
            print(f"🔍 Processing Marengo embeddings with model_type='{metadata_model_type}'")
            
            # Process embeddings
            all_embeddings = []
            for output_key in output_keys:
                obj = s3.get_object(Bucket=S3_BUCKET, Key=output_key)
                embeddings_data = json.loads(obj['Body'].read().decode('utf-8'))

                embeddings = []
                if 'data' in embeddings_data:
                    embeddings = embeddings_data['data']
                    
                    all_embeddings.extend(embeddings)
                    print(f"📖 Processed {len(embeddings)} Marengo embeddings from {output_key}")
            
            print(f"📊 Total Marengo embeddings to process: {len(all_embeddings)}")

            if all_embeddings:
                try:
                    ids = []
                    embeddings = []
                    metadatas = []
                    documents = []
                    
                    for i, item in enumerate(all_embeddings):
                        vec = np.array(item.get('embedding', []))
                        if vec.size == 0:
                            continue
                        norm = np.linalg.norm(vec)
                        if norm == 0:
                            continue
                        normalized_vector = (vec / norm).tolist()
                        ts = item.get('startSec', 0)
                        ts_ffmpeg = to_ffmpeg_ts(ts)
                        
                        doc_id = f"{video_id}_{doc_prefix}_{i}"
                        ids.append(doc_id)
                        embeddings.append(normalized_vector)
                        metadatas.append({
                            'video_id': video_id,
                            'original_key': s3_key,
                            'segment_id': i,
                            'timestamp': ts_ffmpeg,
                            'raw_startSec': ts,
                            'upload_time': str(datetime.now()),
                            'model_type': metadata_model_type  # Use correct model type
                        })
                        documents.append(f"{expected_model_type or 'Marengo'} video segment {i} at {ts_ffmpeg}")

                    # Add to ChromaDB
                    chroma_collection.add(
                        ids=ids,
                        embeddings=embeddings,
                        metadatas=metadatas,
                        documents=documents
                    )
                    
                    model_display = expected_model_type or 'Marengo'
                    print(f"✅ ChromaDB ingestion successful for {video_id}: {len(ids)} segments ({model_display})")
                    st.session_state["indexing_jobs"][video_id] = "Completed"
                    
                    # Clear messages
                    st.session_state["upload_messages"] = [msg for msg in st.session_state["upload_messages"] if msg.get('video_id') != video_id]
                    st.session_state["indexing_messages"] = [msg for msg in st.session_state["indexing_messages"] if msg.get('video_id') != video_id]
                    
                    return True
                except Exception as chroma_error:
                    print(f"❌ ChromaDB ingestion failed: {chroma_error}")
                    st.session_state["indexing_jobs"][video_id] = f"Failed: {str(chroma_error)}"
                    return False
            else:
                print(f"❌ No valid Marengo embeddings to ingest for {video_id}")
                st.session_state["indexing_jobs"][video_id] = "Failed: No valid embeddings"
                return False
        
        # No embeddings found
        if expected_model_type is not None:
            print(f"❌ No embeddings processed for expected model type: {expected_model_type}")
            st.session_state["indexing_jobs"][video_id] = f"Failed: No embeddings found for {expected_model_type}"
        return False

    except Exception as e:
        print(f"❌ Error in check_and_ingest_embeddings: {e}")
        st.session_state["indexing_jobs"][video_id] = f"Failed: {str(e)}"
        return False

def check_indexing_status():
    """Check status of all indexing jobs with progress tracking"""
    updated_count = 0
    progress_data = {}
    
    for video_id in list(st.session_state["indexing_jobs"].keys()):
        old_status = st.session_state["indexing_jobs"][video_id]
        
        if old_status in ["Indexing...", "Waiting"]:
            # Get the S3 key for this video
            videos = s3.list_objects_v2(Bucket=S3_BUCKET, Prefix='videos/')
            s3_key = None
            for obj in videos.get('Contents', []):
                filename = obj['Key'].split('/')[-1]
                if generate_video_id(filename) == video_id:
                    s3_key = obj['Key']
                    break
            
            if s3_key:
                # Check embeddings progress
                embeddings_count = 0
                try:
                    prefixes_to_try = [
                        f'embeddings-output/{video_id}/',
                        f'embeddings-output/{video_id}.mp4/',
                    ]
                    
                    for prefix in prefixes_to_try:
                        objects = s3.list_objects_v2(Bucket=S3_BUCKET, Prefix=prefix)
                        for obj in objects.get('Contents', []):
                            k = obj['Key']
                            if k.startswith(prefix) and k.endswith('output.json'):
                                # Count embeddings in this file
                                try:
                                    emb_obj = s3.get_object(Bucket=S3_BUCKET, Key=k)
                                    embeddings_data = json.loads(emb_obj['Body'].read().decode('utf-8'))
                                    if 'data' in embeddings_data:
                                        embeddings_count += len(embeddings_data['data'])
                                except Exception:
                                    pass
                except Exception:
                    pass
                
                progress_data[video_id] = {
                    'embeddings_found': embeddings_count,
                    'status': old_status
                }
                
                success = check_and_ingest_embeddings(video_id, s3_key, 'TwelveLabs Marengo')
                new_status = st.session_state["indexing_jobs"][video_id]
                
                if old_status != new_status:
                    updated_count += 1
                    print(f"🔄 Status updated for {video_id}: {old_status} → {new_status}")
    
    return updated_count, progress_data

def get_presigned_url(key):
    return s3.generate_presigned_url('get_object', Params={'Bucket': S3_BUCKET, 'Key': key, 'ResponseContentType': 'video/mp4'}, ExpiresIn=3600)


def get_optimal_clip_duration(metadata, similarity_score, max_duration=30):
    """Determine optimal clip duration based on similarity score - simplified and fast"""
    try:
        # Use similarity score to determine duration
        # Higher similarity = longer clip (more relevant content)
        # Lower similarity = shorter clip (less relevant content)
        
        if similarity_score > 0.8:  # Very high similarity
            return min(15, max_duration)  # 15 seconds for very relevant content
        elif similarity_score > 0.6:  # High similarity
            return min(12, max_duration)  # 12 seconds for relevant content
        elif similarity_score > 0.4:  # Medium similarity
            return min(8, max_duration)   # 8 seconds for moderately relevant content
        else:  # Low similarity
            return min(5, max_duration)   # 5 seconds for less relevant content
            
    except Exception as e:
        print(f"Error calculating optimal duration: {e}")
        return min(10, max_duration)  # Fallback to 10 seconds

# ==================== VIDEO PROCESSING FUNCTIONS ====================

def process_video_clip_yolov8(input_path, output_path, enable_labeling=True, enable_pii_blur=True,
                              confidence_threshold=0.5, blur_kernel=35, progress_callback=None,
                              blur_faces=True, blur_plates=True, enable_tracking=True):
    """
    Process video clip using YOLOv8 for object detection and enhanced PII blurring
    
    Args:
        input_path: Path to input video clip
        output_path: Path to save processed video
        enable_labeling: Whether to add object detection labels
        enable_pii_blur: Whether to blur PII (faces and license plates)
        confidence_threshold: YOLOv8 confidence threshold (0-1)
        blur_kernel: Gaussian blur kernel size
        progress_callback: Optional callback for progress updates
        blur_faces: Whether to blur faces (requires enable_pii_blur=True)
        blur_plates: Whether to blur license plates (requires enable_pii_blur=True)
    
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Check if ultralytics is installed
        try:
            from ultralytics import YOLO
            import torch
        except ImportError as e:
            print(f"❌ YOLOv8 dependencies not installed: {e}")
            print(f"💡 Installing ultralytics package...")
            import subprocess
            subprocess.check_call(['pip', 'install', 'ultralytics', 'torch', 'torchvision'])
            from ultralytics import YOLO
            import torch
        
        print(f"🚀 Starting YOLOv8 video processing...")
        print(f"   Input: {input_path}")
        print(f"   Output: {output_path}")
        
        # Verify input file exists
        if not os.path.exists(input_path):
            raise Exception(f"Input file not found: {input_path}")
        
        # Load YOLOv8 model (will auto-download on first use)
        print(f"📦 Loading YOLOv8 model...")
        model = YOLO('yolov8n.pt')  # nano model for speed
        print(f"✅ Model loaded successfully")
        
        # Load face detection model if needed
        face_detector = None
        if enable_pii_blur and blur_faces:
            try:
                import mediapipe as mp
                mp_face_detection = mp.solutions.face_detection
                face_detector = mp_face_detection.FaceDetection(
                    model_selection=0,  # 0 for short-range (< 2m), 1 for full-range
                    min_detection_confidence=0.5
                )
                print(f"✅ MediaPipe face detection loaded")
            except ImportError:
                print(f"⚠️ MediaPipe not installed, using basic face detection")
                face_detector = None
            except Exception as e:
                print(f"⚠️ Face detection init failed: {e}")
                face_detector = None
        
        # Open video
        cap = cv2.VideoCapture(input_path)
        if not cap.isOpened():
            raise Exception(f"Cannot open input video: {input_path}")
        
        # Get video properties
        fps = cap.get(cv2.CAP_PROP_FPS) or 25.0
        w = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        h = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT) or 0)
        
        # Use temporary AVI file for processing (more reliable)
        temp_output = output_path.replace('.mp4', '_temp.avi')
        fourcc = cv2.VideoWriter_fourcc(*"XVID")
        out = cv2.VideoWriter(temp_output, fourcc, fps, (w, h))
        
        if not out.isOpened():
            raise Exception("Failed to initialize video writer")
        
        print(f"📹 Video: {w}x{h} @ {fps}fps, {total_frames} frames")
        
        # Define classes for safety monitoring
        vehicle_classes = ['car', 'truck', 'bus', 'motorcycle', 'bicycle']
        person_classes = ['person']
        
        frame_idx = 0
        detections_count = {'vehicles': 0, 'persons': 0, 'total_frames': 0}
        track_history = {}  # Store tracking history for trajectory visualization
        
        with tqdm(total=total_frames if total_frames > 0 else None,
                 desc="Processing with YOLOv8", leave=False) as pbar:
            
            while True:
                ok, frame = cap.read()
                if not ok:
                    break
                frame_idx += 1
                
                # Run YOLOv8 inference with tracking
                if enable_tracking:
                    results = model.track(frame, conf=confidence_threshold, verbose=False, persist=True)
                else:
                    results = model(frame, conf=confidence_threshold, verbose=False)
                
                # Process detections
                for result in results:
                    boxes = result.boxes
                    
                    for box in boxes:
                        # Get box coordinates
                        x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                        conf = float(box.conf[0])
                        cls = int(box.cls[0])
                        class_name = model.names[cls]
                        
                        x1, y1, x2, y2 = int(x1), int(y1), int(x2), int(y2)
                        
                        # Get tracking ID if available
                        track_id = None
                        if enable_tracking and hasattr(box, 'id') and box.id is not None:
                            track_id = int(box.id[0])
                            
                            # Store trajectory
                            center_x = (x1 + x2) // 2
                            center_y = (y1 + y2) // 2
                            
                            if track_id not in track_history:
                                track_history[track_id] = []
                            track_history[track_id].append((center_x, center_y))
                            
                            # Keep only last 30 points for trajectory
                            if len(track_history[track_id]) > 30:
                                track_history[track_id].pop(0)
                        
                        # Object detection labeling
                        if enable_labeling and class_name in vehicle_classes + person_classes:
                            # Draw bounding box
                            color = (0, 255, 0) if class_name in vehicle_classes else (255, 0, 0)
                            cv2.rectangle(frame, (x1, y1), (x2, y2), color, 2)
                            
                            # Draw label with tracking ID
                            if track_id is not None:
                                label_text = f"ID:{track_id} {class_name} {conf:.2f}"
                            else:
                                label_text = f"{class_name} {conf:.2f}"
                            
                            (tw, th), _ = cv2.getTextSize(label_text, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 1)
                            y = max(10, y1-5)
                            cv2.rectangle(frame, (x1, y-th-6), (x1+tw+4, y), color, -1)
                            cv2.putText(frame, label_text, (x1+2, y-3),
                                      cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1, cv2.LINE_AA)
                            
                            # Draw trajectory if tracking enabled
                            if enable_tracking and track_id in track_history and len(track_history[track_id]) > 1:
                                points = track_history[track_id]
                                for i in range(1, len(points)):
                                    # Draw line between consecutive points
                                    cv2.line(frame, points[i-1], points[i], color, 2)
                                # Draw circle at current position
                                cv2.circle(frame, points[-1], 4, color, -1)
                            
                            # Count detections
                            if class_name in vehicle_classes:
                                detections_count['vehicles'] += 1
                            elif class_name in person_classes:
                                detections_count['persons'] += 1
                        
                        # License plate detection (cars, trucks, buses)
                        if enable_pii_blur and blur_plates and class_name in ['car', 'truck', 'bus']:
                            # Blur bottom 20% of vehicle (likely license plate area)
                            plate_height = int((y2 - y1) * 0.2)
                            plate_y1 = y2 - plate_height
                            plate_y2 = y2
                            frame = gaussian_blur_rect(frame, x1, plate_y1, x2-x1, plate_height, blur_kernel)
                
                # Enhanced face detection using MediaPipe
                if enable_pii_blur and blur_faces and face_detector:
                    try:
                        # Convert BGR to RGB for MediaPipe
                        rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                        face_results = face_detector.process(rgb_frame)
                        
                        if face_results.detections:
                            for detection in face_results.detections:
                                bboxC = detection.location_data.relative_bounding_box
                                # Convert relative coordinates to absolute
                                fx1 = int(bboxC.xmin * w)
                                fy1 = int(bboxC.ymin * h)
                                fw = int(bboxC.width * w)
                                fh = int(bboxC.height * h)
                                
                                # Add padding for better coverage
                                padding = int(fw * 0.2)
                                fx1 = max(0, fx1 - padding)
                                fy1 = max(0, fy1 - padding)
                                fw = min(w - fx1, fw + 2 * padding)
                                fh = min(h - fy1, fh + 2 * padding)
                                
                                # Blur face region
                                frame = gaussian_blur_rect(frame, fx1, fy1, fw, fh, blur_kernel)
                                detections_count['faces_blurred'] = detections_count.get('faces_blurred', 0) + 1
                    except Exception as e:
                        # Silently continue if face detection fails
                        pass
                
                detections_count['total_frames'] += 1
                out.write(frame)
                pbar.update(1)
                
                # Progress callback
                if progress_callback and frame_idx % 20 == 0:
                    progress = (frame_idx / total_frames) * 100
                    progress_callback(progress, f"Processing frame {frame_idx}/{total_frames}")
        
        cap.release()
        out.release()
        
        # Print detection summary
        print(f"✅ YOLOv8 processing complete!")
        print(f"   Vehicles detected: {detections_count['vehicles']}")
        print(f"   Persons detected: {detections_count['persons']}")
        if 'faces_blurred' in detections_count:
            print(f"   Faces blurred: {detections_count['faces_blurred']}")
        if enable_tracking:
            print(f"   Unique tracks: {len(track_history)}")
        print(f"   Frames processed: {detections_count['total_frames']}")
        
        # Convert AVI to MP4 using FFmpeg for web compatibility
        print(f"🔄 Converting to MP4 format...")
        try:
            import subprocess
            cmd = [
                'ffmpeg', '-y', '-i', temp_output,
                '-c:v', 'libx264',
                '-preset', 'fast',
                '-crf', '23',
                '-pix_fmt', 'yuv420p',
                '-movflags', '+faststart',
                output_path
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
            
            if result.returncode != 0:
                print(f"⚠️ FFmpeg warning: {result.stderr}")
            
            # Clean up temp file
            if os.path.exists(temp_output):
                os.remove(temp_output)
            
            # Verify final output
            if os.path.exists(output_path) and os.path.getsize(output_path) > 0:
                file_size = os.path.getsize(output_path) / (1024 * 1024)  # MB
                print(f"✅ Processed video saved: {output_path} ({file_size:.2f} MB)")
                return True
            else:
                print(f"❌ Failed to save processed video (0 bytes)")
                return False
                
        except subprocess.TimeoutExpired:
            print(f"❌ FFmpeg conversion timeout")
            return False
        except Exception as ffmpeg_error:
            print(f"❌ FFmpeg conversion error: {ffmpeg_error}")
            # Try to use temp file as fallback
            if os.path.exists(temp_output) and os.path.getsize(temp_output) > 0:
                import shutil
                shutil.move(temp_output, output_path)
                print(f"⚠️ Using AVI format (FFmpeg failed)")
                return True
            return False
            
    except Exception as e:
        print(f"❌ YOLOv8 processing error: {e}")
        import traceback
        traceback.print_exc()
        return False


def gaussian_blur_rect(img, x, y, w, h, k):
    """Apply Gaussian blur to a rectangular region"""
    H, W = img.shape[:2]
    x = max(0, int(x)); y = max(0, int(y))
    w = max(1, int(w)); h = max(1, int(h))
    x2 = min(W, x + w); y2 = min(H, y + h)
    if k % 2 == 0: k += 1
    roi = img[y:y2, x:x2]
    if roi.size == 0:
        return img
    img[y:y2, x:x2] = cv2.GaussianBlur(roi, (k, k), 0)
    return img


def process_video_clip_rekognition(input_path, output_path, enable_labeling=False, enable_pii_blur=True, 
                                  confidence_threshold=0.7, blur_kernel=35, progress_callback=None):
    """
    Process video clip using AWS Rekognition for object detection and PII blurring
    
    Args:
        input_path: Path to input video clip
        output_path: Path to save processed video
        enable_labeling: Whether to add object detection labels
        enable_pii_blur: Whether to blur faces and text
        confidence_threshold: Rekognition confidence threshold
        blur_kernel: Gaussian blur kernel size
    """
    try:
        cap = cv2.VideoCapture(input_path)
        if not cap.isOpened():
            raise Exception(f"Cannot open input video: {input_path}")
        
        # Get video properties
        fps = cap.get(cv2.CAP_PROP_FPS) or 25.0
        w = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        h = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT) or 0)
        
        # Use mp4v codec for processing (will be re-encoded with FFmpeg later)
        fourcc = cv2.VideoWriter_fourcc(*"mp4v")
        out = cv2.VideoWriter(output_path, fourcc, fps, (w, h))
        
        if not out.isOpened():
            raise Exception("Failed to initialize video writer")
        
        # Process frames using AWS Rekognition
        # OPTIMIZATION: Process only every Nth frame to speed up (5x faster)
        frame_skip = 5  # Process every 5th frame
        frame_idx = 0
        
        with tqdm(total=total_frames if total_frames > 0 else None, 
                 desc="Processing video with Rekognition", leave=False) as pbar:
            
            while True:
                ok, frame = cap.read()
                if not ok:
                    break
                frame_idx += 1
                
                # Skip frames for faster processing
                should_process = (frame_idx % frame_skip == 0)
                
                # Convert frame to bytes for Rekognition (only if processing this frame)
                if should_process and (enable_labeling or enable_pii_blur):
                    _, buffer = cv2.imencode('.jpg', frame)
                    frame_bytes = buffer.tobytes()
                else:
                    frame_bytes = None
                
                # AWS Rekognition object detection
                if enable_labeling and should_process and frame_bytes:
                    try:
                        response = rekognition.detect_labels(
                            Image={'Bytes': frame_bytes},
                            MinConfidence=confidence_threshold * 100  # Rekognition uses 0-100 scale
                        )
                        
                        # Draw bounding boxes for detected objects
                        for label in response['Labels']:
                            if label['Name'].lower() in ['car', 'truck', 'bus', 'motorcycle', 'bicycle', 'person', 'vehicle']:
                                for instance in label.get('Instances', []):
                                    if instance['Confidence'] >= confidence_threshold * 100:
                                        bbox = instance['BoundingBox']
                                        x1 = int(bbox['Left'] * w)
                                        y1 = int(bbox['Top'] * h)
                                        x2 = int((bbox['Left'] + bbox['Width']) * w)
                                        y2 = int((bbox['Top'] + bbox['Height']) * h)
                                        
                                        # Draw green bounding box
                                        cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
                                        
                                        # Draw label with confidence
                                        label_text = f"{label['Name']} {instance['Confidence']:.1f}%"
                                        (tw, th), _ = cv2.getTextSize(label_text, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 1)
                                        y = max(10, y1-5)
                                        cv2.rectangle(frame, (x1, y-th-6), (x1+tw+4, y), (0, 255, 0), -1)
                                        cv2.putText(frame, label_text, (x1+2, y-3), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 1, cv2.LINE_AA)
                                        
                                        if frame_idx % 30 == 0:  # Print every 30th frame
                                            print(f"🎯 Detected {label['Name']} {instance['Confidence']:.1f}% at ({x1},{y1},{x2},{y2})")
                    except Exception as e:
                        print(f"⚠️ Rekognition object detection failed on frame {frame_idx}: {e}")
                
                # AWS Rekognition face detection and blurring
                if enable_pii_blur and should_process and frame_bytes:
                    try:
                        # Detect faces
                        face_response = rekognition.detect_faces(
                            Image={'Bytes': frame_bytes},
                            Attributes=['ALL']
                        )
                        
                        for face in face_response['FaceDetails']:
                            if face['Confidence'] >= confidence_threshold * 100:
                                bbox = face['BoundingBox']
                                x1 = int(bbox['Left'] * w)
                                y1 = int(bbox['Top'] * h)
                                x2 = int((bbox['Left'] + bbox['Width']) * w)
                                y2 = int((bbox['Top'] + bbox['Height']) * h)
                                
                                # Blur face region
                                frame = gaussian_blur_rect(frame, x1, y1, x2-x1, y2-y1, blur_kernel)
                                
                                if frame_idx % 30 == 0:  # Print every 30th frame
                                    print(f"👤 Blurred face {face['Confidence']:.1f}% at ({x1},{y1},{x2},{y2})")
                        
                        # Detect text (for license plates, signs, etc.)
                        text_response = rekognition.detect_text(
                            Image={'Bytes': frame_bytes}
                        )
                        
                        for text in text_response['TextDetections']:
                            if text['Confidence'] >= confidence_threshold * 100 and text['Type'] == 'LINE':
                                bbox = text['Geometry']['BoundingBox']
                                x1 = int(bbox['Left'] * w)
                                y1 = int(bbox['Top'] * h)
                                x2 = int((bbox['Left'] + bbox['Width']) * w)
                                y2 = int((bbox['Top'] + bbox['Height']) * h)
                                
                                # Blur text region
                                frame = gaussian_blur_rect(frame, x1, y1, x2-x1, y2-y1, blur_kernel)
                                
                                if frame_idx % 30 == 0:  # Print every 30th frame
                                    print(f"📝 Blurred text '{text['DetectedText']}' {text['Confidence']:.1f}% at ({x1},{y1},{x2},{y2})")
                    except Exception as e:
                        print(f"⚠️ Rekognition PII detection failed on frame {frame_idx}: {e}")
                
                out.write(frame)
                pbar.update(1)
                
                # Update progress callback if provided
                if progress_callback and frame_idx % 20 == 0:
                    progress = (frame_idx / total_frames) * 100
                    progress_callback(progress, f"Processing frame {frame_idx}/{total_frames}")
        
        cap.release()
        out.release()
        
        # Verify output file was created
        if os.path.exists(output_path) and os.path.getsize(output_path) > 0:
            print(f"✅ Processed video saved with Rekognition: {output_path}")
            
            # Re-encode with FFmpeg for web compatibility
            temp_output = output_path.replace('.mp4', '_web.mp4')
            try:
                cmd = [
                    'ffmpeg', '-y', '-i', output_path,
                    '-c:v', 'libx264', '-c:a', 'aac',
                    '-preset', 'fast', '-crf', '23',
                    '-movflags', '+faststart',  # Optimize for web streaming
                    temp_output
                ]
                
                result = subprocess.run(cmd, capture_output=True, text=True, check=True)
                
                if os.path.exists(temp_output):
                    # Replace original with web-compatible version
                    os.replace(temp_output, output_path)
                    print(f"✅ Re-encoded video for web compatibility: {output_path}")
                else:
                    print(f"⚠️ FFmpeg re-encoding failed, using original: {output_path}")
                    
            except subprocess.CalledProcessError as e:
                print(f"⚠️ FFmpeg re-encoding failed: {e.stderr}, using original: {output_path}")
                # Clean up temp file if it exists
                if os.path.exists(temp_output):
                    os.remove(temp_output)
            
            return True
        else:
            print(f"❌ Failed to create processed video: {output_path}")
            return False
            
    except Exception as e:
        print(f"❌ Rekognition video processing failed: {e}")
        return False


def s3_object_exists(key):
    """Check if an S3 object exists"""
    try:
        s3.head_object(Bucket=S3_BUCKET, Key=key)
        return True
    except:
        return False

def upload_json_to_s3(data, key):
    """Upload JSON data to S3"""
    import json
    s3.put_object(
        Bucket=S3_BUCKET,
        Key=key,
        Body=json.dumps(data, default=str),
        ContentType='application/json'
    )

def download_json_from_s3(key):
    """Download JSON data from S3"""
    import json
    response = s3.get_object(Bucket=S3_BUCKET, Key=key)
    return json.loads(response['Body'].read().decode('utf-8'))

def sanitize_s3_key(filename):
    """Sanitize filename for S3 - limit length and remove special chars"""
    import re
    # Remove or replace problematic characters
    filename = re.sub(r'[^\w\s\-\.]', '_', filename)
    # Limit filename length (keep extension)
    name, ext = os.path.splitext(filename)
    if len(name) > 100:
        name = name[:100]
    return f"{name}{ext}"

def upload_video_to_s3(local_path, s3_key):
    """Upload video file to S3 with proper content type and validation"""
    # Validate file exists and has content
    if not os.path.exists(local_path):
        raise FileNotFoundError(f"Video file not found: {local_path}")
    
    file_size = os.path.getsize(local_path)
    if file_size == 0:
        raise ValueError(f"Video file is empty (0 bytes): {local_path}")
    
    if file_size < 1024:  # Less than 1KB is suspicious for a video
        print(f"⚠️ Warning: Video file is very small ({file_size} bytes): {local_path}")
    
    # Upload with explicit content type to avoid MIME type detection issues
    s3.upload_file(
        local_path, 
        S3_BUCKET, 
        s3_key,
        ExtraArgs={'ContentType': 'video/mp4'}
    )
    
    # Verify upload succeeded
    try:
        response = s3.head_object(Bucket=S3_BUCKET, Key=s3_key)
        uploaded_size = response.get('ContentLength', 0)
        if uploaded_size != file_size:
            raise ValueError(f"Upload size mismatch: local={file_size}, s3={uploaded_size}")
        print(f"✅ Uploaded {file_size} bytes to S3: {s3_key}")
    except Exception as e:
        raise Exception(f"Failed to verify S3 upload: {e}")
    
    return f"s3://{S3_BUCKET}/{s3_key}"

def get_s3_presigned_url(s3_key, expiration=3600):
    """Get presigned URL for S3 object with cache-busting"""
    return s3.generate_presigned_url(
        'get_object',
        Params={
            'Bucket': S3_BUCKET, 
            'Key': s3_key,
            'ResponseContentType': 'video/mp4',
            'ResponseCacheControl': 'no-cache'
        },
        ExpiresIn=expiration
    )


def deduplicate_results(results, time_threshold=1.5):
    """Remove duplicate clips with similar timestamps - improved algorithm"""
    if not results['ids'][0]:
        return results
    
    # Get all results with metadata
    all_results = []
    for i, (doc_id, distance, metadata) in enumerate(zip(results['ids'][0], results['distances'][0], results['metadatas'][0])):
        all_results.append({
            'doc_id': doc_id,
            'distance': distance,
            'metadata': metadata,
            'start_sec': metadata.get('raw_startSec', 0),
            'video_id': metadata.get('video_id', ''),
            'original_key': metadata.get('original_key', ''),
            'similarity': 1 - distance  # Convert distance to similarity
        })
    
    # Sort by similarity (higher similarity first)
    all_results.sort(key=lambda x: x['similarity'], reverse=True)
    
    # Group by video_id for better deduplication
    video_groups = {}
    for result in all_results:
        video_id = result['video_id']
        if video_id not in video_groups:
            video_groups[video_id] = []
        video_groups[video_id].append(result)
    
    # Select best clips from each video group
    unique_results = []
    for video_id, video_clips in video_groups.items():
        # Sort clips in this video by timestamp
        video_clips.sort(key=lambda x: x['start_sec'])
        
        selected_clips = []
        for clip in video_clips:
            # Check if this clip is too close to any already selected clip from this video
            too_close = False
            for selected in selected_clips:
                if abs(selected['start_sec'] - clip['start_sec']) < time_threshold:
                    # If too close, keep the one with higher similarity
                    if clip['similarity'] > selected['similarity']:
                        selected_clips.remove(selected)
                        selected_clips.append(clip)
                    too_close = True
                    break
            
            if not too_close:
                selected_clips.append(clip)
        
        unique_results.extend(selected_clips)
    
    # Sort final results by similarity again
    unique_results.sort(key=lambda x: x['similarity'], reverse=True)
    
    # Reconstruct results in ChromaDB format
    if unique_results:
        deduplicated_results = {
            'ids': [[r['doc_id'] for r in unique_results]],
            'distances': [[r['distance'] for r in unique_results]],
            'metadatas': [[r['metadata'] for r in unique_results]]
        }
        return deduplicated_results
    else:
        return results

def save_rating_to_s3(rating_data):
    """Save user rating to S3 for analytics"""
    try:
        s3_key = f"ratings/{rating_data['timestamp']}_{rating_data['query_hash']}.json"
        s3.put_object(
            Bucket=S3_BUCKET,
            Key=s3_key,
            Body=json.dumps(rating_data),
            ContentType='application/json'
        )
        print(f"✅ Rating saved to S3: {s3_key}")
        return True
    except Exception as e:
        print(f"❌ Failed to save rating to S3: {e}")
        return False

def load_ratings_from_s3():
    """Load all ratings from S3 for analytics"""
    try:
        ratings = []
        response = s3.list_objects_v2(Bucket=S3_BUCKET, Prefix="ratings/")
        
        for obj in response.get('Contents', []):
            if obj['Key'].endswith('.json'):
                rating_obj = s3.get_object(Bucket=S3_BUCKET, Key=obj['Key'])
                rating_data = json.loads(rating_obj['Body'].read())
                ratings.append(rating_data)
        
        return ratings
    except Exception as e:
        print(f"❌ Failed to load ratings from S3: {e}")
        return []

def get_model_analytics():
    """Get analytics data comparing models"""
    ratings = load_ratings_from_s3()
    
    if not ratings:
        return None
    
    df = pd.DataFrame(ratings)
    
    # Group by model and calculate statistics
    model_stats = df.groupby('model_type').agg({
        'rating': ['count', 'mean', 'std'],
        'similarity_score': 'mean'
    }).round(3)
    
    return {
        'dataframe': df,
        'model_stats': model_stats,
        'total_ratings': len(ratings)
    }

def get_video_indexed_models(video_id):
    """Get list of models that have been used to index a video"""
    indexed_models = []
    
    try:
        # Check ChromaDB for existing embeddings
        existing = chroma_collection.get(where={"video_id": video_id}, limit=100)
        print(f"🔍 Checking indexed models for {video_id}: found {len(existing['ids'])} embeddings")
        
        if existing['ids']:
            # Get unique model types from metadata
            model_types = set()
            for metadata in existing['metadatas']:
                model_type = metadata.get('model_type', 'unknown')
                print(f"🔍 Found model_type: {model_type}")
                if model_type == 'nova':
                    model_types.add('AWS Nova Premier')
                elif model_type == 'marengo':
                    model_types.add('TwelveLabs Marengo')
            indexed_models = list(model_types)
            print(f"🔍 Indexed models for {video_id}: {indexed_models}")
    except Exception as e:
        print(f"Error checking indexed models for {video_id}: {e}")
    
    return indexed_models

def get_available_models_for_video(video_id):
    """Get list of models that can be used to index a video (not yet used)"""
    all_models = ['AWS Nova Premier', 'TwelveLabs Marengo']
    indexed_models = get_video_indexed_models(video_id)
    available_models = [model for model in all_models if model not in indexed_models]
    return available_models, indexed_models

def search_with_cache(query, top_k, chroma_collection):
    """Search with S3 caching for better performance and persistence"""
    # Get current model selection
    current_model = st.session_state.get('embedding_model', 'AWS Nova Premier')
    
    # Create S3 cache key with model info
    cache_key = hashlib.md5(f"{query}_{top_k}_{current_model}".encode()).hexdigest()
    s3_cache_key = f"search-cache/{cache_key}/results.json"
    
    # Check S3 cache first
    if s3_object_exists(s3_cache_key):
        try:
            cached_data = download_json_from_s3(s3_cache_key)
            print(f"🎯 Using S3 cached results for query: {query} (model: {current_model})")
            return cached_data['results'], cached_data['query_embedding']
        except Exception as e:
            print(f"⚠️ S3 cache read failed: {e}")
    
    # Perform fresh search
    print(f"🔍 Performing fresh search for query: {query} (model: {current_model})")
    
    # Get text embedding based on selected model
    if current_model == 'AWS Nova Premier':
        # Use Nova Premier for text embedding
        query_embed = generate_nova_text_embedding(query)
        if query_embed is None:
            st.error("Failed to generate Nova text embedding")
            return {'ids': [[]], 'distances': [[]], 'metadatas': [[]]}, None
        query_vector = np.array(query_embed)
        normalized_query = query_vector / np.linalg.norm(query_vector)
    else:
        # Use Marengo for text embedding
        model_id = 'us.twelvelabs.marengo-embed-2-7-v1:0'
        body = json.dumps({
            'inputType': 'text',
            'inputText': query
        })
        response = bedrock.invoke_model(
            modelId=model_id,
            body=body,
            contentType='application/json',
            accept='application/json'
        )
        query_embed = json.loads(response['body'].read())['data'][0]['embedding']
        query_vector = np.array(query_embed)
        normalized_query = query_vector / np.linalg.norm(query_vector)

    # Search ChromaDB with more results to allow for deduplication
    search_k = min(top_k * 5, 100)  # Get 5x more results for better deduplication
    results = chroma_collection.query(
        query_embeddings=[normalized_query.tolist()],
        n_results=search_k
    )
    
    # Deduplicate results with improved algorithm
    results = deduplicate_results(results, time_threshold=1.5)  # Reduced threshold
    
    # Limit to requested top_k
    if results['ids'][0] and len(results['ids'][0]) > top_k:
        results = {
            'ids': [results['ids'][0][:top_k]],
            'distances': [results['distances'][0][:top_k]],
            'metadatas': [results['metadatas'][0][:top_k]]
        }
    
    # Cache results to S3
    try:
        cache_data = {
            'results': results,
            'query_embedding': normalized_query.tolist(),
            'model_used': current_model
        }
        upload_json_to_s3(cache_data, s3_cache_key)
        print(f"💾 Cached search results to S3 for query: {query} (model: {current_model})")
    except Exception as e:
        print(f"⚠️ S3 cache write failed: {e}")
    
    return results, normalized_query.tolist()

# Main tabs for the application
main_tabs = st.tabs(["🔍 Search Videos", "📁 Uploaded Videos", "📤 Add New Video", "📊 Model Analytics"])

# Tab 1: Search Videos
with main_tabs[0]:
    st.subheader('Search Videos')
    
    # Advanced filters (collapsible)
    with st.expander("⚙️ Advanced Filters", expanded=False):
        top_k = st.slider('Results (k)', min_value=1, max_value=20, value=5, step=1)
        clip_seconds = st.slider('Clip duration (seconds)', min_value=5, max_value=120, value=10, step=5)

    # Video Processing Options
    with st.expander("🎬 Video Processing Options", expanded=False):
        # Detection engine selection
        detection_engine = st.radio(
            "Detection Engine",
            options=["YOLOv8 (Recommended)", "AWS Rekognition"],
            index=0,
            help="YOLOv8: Faster, local processing, better accuracy. Rekognition: Cloud-based, slower but proven."
        )
        
        st.divider()
        
        col1, col2 = st.columns(2)
        with col1:
            enable_labeling = st.checkbox('🏷️ Object Labeling', value=True, 
                                        help='Add bounding boxes and labels for detected vehicles and persons')
            if enable_labeling:
                if "YOLOv8" in detection_engine:
                    confidence_threshold = st.slider('Detection Confidence', min_value=0.3, max_value=0.9, value=0.5, step=0.05,
                                                help='Higher values = fewer but more confident detections')
                else:
                    confidence_threshold = st.slider('Detection Confidence', min_value=0.5, max_value=0.95, value=0.7, step=0.05,
                                                help='Higher values = fewer but more confident detections')
        
        with col2:
            enable_pii_blur = st.checkbox('🔒 PII Protection', value=True,
                                        help='Blur faces and sensitive information')
            if enable_pii_blur:
                blur_intensity = st.slider('Blur Intensity', min_value=15, max_value=75, value=35, step=5,
                                         help='Higher values = more blur')
                
                # Enhanced PII options
                st.write("**PII Options:**")
                col_face, col_plate = st.columns(2)
                with col_face:
                    blur_faces = st.checkbox('👤 Blur Faces', value=True,
                                           help='Use MediaPipe for precise face detection')
                with col_plate:
                    blur_plates = st.checkbox('🚗 Blur License Plates', value=True,
                                            help='Blur vehicle license plates automatically')
        
        # Object tracking option (YOLOv8 only)
        enable_tracking = False
        if "YOLOv8" in detection_engine:
            enable_tracking = st.checkbox('🎯 Object Tracking', value=True,
                                        help='Track objects across frames with unique IDs and trajectories')
        
        # Show engine info
        if "YOLOv8" in detection_engine:
            st.info("🚀 YOLOv8: Real-time object detection (~60x faster than Rekognition, runs locally)")
        else:
            st.warning("⚠️ AWS Rekognition: Slower processing (~1.8s per frame), cloud-based")

    # Store in session state
    st.session_state['top_k'] = top_k
    st.session_state['clip_seconds'] = clip_seconds

    # Use session state values or defaults
    top_k = st.session_state.get('top_k', 5)
    clip_seconds = st.session_state.get('clip_seconds', 10)

    # Safety-specific search presets
    with st.expander("🚨 Quick Safety Searches", expanded=False):
        st.write("**Common Safety Scenarios:**")
        
        safety_queries = {
            "🚗 Distracted Driving": "driver using phone or distracted while driving",
            "⚡ Aggressive Driving": "aggressive driving harsh braking sudden lane changes",
            "🚶 Pedestrian Risk": "pedestrian crossing near miss close call",
            "🚦 Traffic Violations": "running red light stop sign violation",
            "🌧️ Weather Hazards": "driving in rain fog poor visibility conditions",
            "🚙 Following Too Close": "tailgating unsafe following distance",
            "💨 Speeding": "excessive speed fast driving speeding",
            "🛑 Hard Braking": "harsh braking sudden stop emergency brake",
            "🔄 Lane Violations": "improper lane change weaving crossing lanes",
            "🌙 Night Driving": "driving at night dark conditions low visibility"
        }
        
        cols = st.columns(2)
        for i, (label, query_text) in enumerate(safety_queries.items()):
            with cols[i % 2]:
                if st.button(label, key=f"preset_{i}", use_container_width=True):
                    st.session_state['preset_query'] = query_text
                    st.rerun()
    
    # Get query from preset or manual input
    default_query = st.session_state.pop('preset_query', '')
    query = st.text_input('Enter query (e.g., "driver distracted at night")', value=default_query)
    
    # Advanced search filters
    with st.expander("🔍 Advanced Filters", expanded=False):
        col_filter1, col_filter2 = st.columns(2)
        
        with col_filter1:
            st.write("**Detection Filters:**")
            filter_vehicles = st.checkbox("🚗 Videos with vehicles", value=False,
                                         help="Only show videos with vehicle detections")
            filter_pedestrians = st.checkbox("🚶 Videos with pedestrians", value=False,
                                            help="Only show videos with pedestrian detections")
            filter_high_risk = st.checkbox("⚠️ High risk only", value=False,
                                          help="Only show videos with risk score > 7")
        
        with col_filter2:
            st.write("**Time Filters:**")
            time_of_day = st.selectbox("Time of Day", 
                                       ["Any", "Morning (6AM-12PM)", "Afternoon (12PM-6PM)", 
                                        "Evening (6PM-9PM)", "Night (9PM-6AM)"])
            
            date_range = st.radio("Date Range", 
                                 ["All Time", "Last 24 Hours", "Last Week", "Last Month"])
    
    # Store filters in session state
    st.session_state['filter_vehicles'] = filter_vehicles if 'filter_vehicles' in locals() else False
    st.session_state['filter_pedestrians'] = filter_pedestrians if 'filter_pedestrians' in locals() else False
    st.session_state['filter_high_risk'] = filter_high_risk if 'filter_high_risk' in locals() else False
    st.session_state['time_of_day'] = time_of_day if 'time_of_day' in locals() else "Any"
    st.session_state['date_range'] = date_range if 'date_range' in locals() else "All Time"
    
    # Stop search button
    if st.session_state.get('search_running', False):
        col1, col2 = st.columns([1, 4])
        with col1:
            if st.button('🛑 Stop Search', type='secondary'):
                st.session_state['search_running'] = False
                st.rerun()
        with col2:
            st.warning("Search in progress... Click 'Stop Search' to cancel.")
    
    if not query:
        st.info("💡 Enter a search query above to find matching video clips. Search results will appear in this tab.")

    if query and not st.session_state.get('search_cancelled', False):
        # Set search running state
        st.session_state['search_running'] = True
        st.session_state['search_cancelled'] = False
        
        # Note: Search results will be displayed in this tab
        # Users can manually switch between tabs to view uploaded videos
        
        with st.spinner('Searching...'):
            try:
            # Use cached search for better performance - returns both results and query embedding
                search_result = search_with_cache(query, top_k, chroma_collection)
            
            # Handle both old and new return formats
                if isinstance(search_result, tuple) and len(search_result) == 2:
                    results, query_embedding = search_result
                else:
                # Fallback for old format
                    results = search_result
                query_embedding = None

                st.subheader('Matching Clips')
            
                if results['ids'][0]:  # Check if we have results
                    clip_cols = st.columns(2)
                
                for idx, (doc_id, distance, metadata) in enumerate(zip(results['ids'][0], results['distances'][0], results['metadatas'][0])):
                    original_key = metadata['original_key']
                    start_time = metadata['timestamp']
                    
                    # Create S3 cache keys for this query and clip
                    query_cache_key = hashlib.md5(f"{query}_{clip_seconds}".encode()).hexdigest()
                    clip_cache_prefix = f"search-cache/{query_cache_key}/clips/"
                    
                    start_sec = metadata.get('raw_startSec', 0)
                    if not isinstance(start_sec, (int, float)):
                        start_sec = 0
                    
                    similarity_score = 1 - distance
                    optimal_duration = get_optimal_clip_duration(metadata, similarity_score, clip_seconds)
                    
                    # S3 keys for cached clips
                    original_clip_key = f"{clip_cache_prefix}original-{metadata['segment_id']}-{idx}.mp4"
                    processed_clip_key = f"{clip_cache_prefix}processed-{metadata['segment_id']}-{idx}.mp4"
                    
                    with clip_cols[idx % 2]:
                        try:
                            # Check if we have cached clips in S3
                            original_clip_url = None
                            processed_clip_url = None
                            
                            if s3_object_exists(original_clip_key):
                                print(f"🎯 Using cached original clip from S3")
                                original_clip_url = get_s3_presigned_url(original_clip_key)
                            else:
                                # Generate and cache original clip
                                print(f"🔄 Generating original clip for S3 cache")
                                temp_original = f'/tmp/original-{metadata["video_id"]}-{idx}.mp4'
                                clip_path = f'/tmp/clip-{metadata["segment_id"]}-{idx}.mp4'
                                
                                try:
                                    # Download original from S3 to temp
                                    s3.download_file(S3_BUCKET, original_key, temp_original)
                                    
                                    # Validate downloaded file
                                    if not os.path.exists(temp_original) or os.path.getsize(temp_original) == 0:
                                        raise ValueError(f"Downloaded video is empty or missing: {temp_original}")
                                    
                                    print(f"📥 Downloaded {os.path.getsize(temp_original)} bytes from S3")
                                    
                                    # FFmpeg command to extract clip with better error handling
                                    cmd = [
                                        'ffmpeg', '-y', '-i', temp_original,
                                        '-ss', str(start_sec), '-t', str(optimal_duration),
                                        '-c:v', 'libx264', '-c:a', 'aac',
                                        '-preset', 'fast', '-crf', '23',
                                        '-avoid_negative_ts', 'make_zero',
                                        '-loglevel', 'error',  # Only show errors
                                        clip_path
                                    ]
                                    
                                    print(f"🎬 Running FFmpeg: {' '.join(cmd)}")
                                    result = subprocess.run(cmd, capture_output=True, text=True, check=True)
                                    
                                    # Validate generated clip
                                    if not os.path.exists(clip_path):
                                        raise FileNotFoundError(f"FFmpeg did not create output file: {clip_path}")
                                    
                                    clip_size = os.path.getsize(clip_path)
                                    if clip_size == 0:
                                        raise ValueError(f"FFmpeg created empty file: {clip_path}")
                                    
                                    print(f"✅ Generated clip: {clip_size} bytes")
                                    
                                    # Upload to S3 cache with validation
                                    upload_video_to_s3(clip_path, original_clip_key)
                                    original_clip_url = get_s3_presigned_url(original_clip_key)
                                    print(f"💾 Cached original clip to S3")
                                    
                                except subprocess.CalledProcessError as e:
                                    error_msg = f"FFmpeg failed: {e.stderr}"
                                    print(f"❌ {error_msg}")
                                    st.error(error_msg)
                                    continue
                                except Exception as e:
                                    error_msg = f"Clip generation failed: {str(e)}"
                                    print(f"❌ {error_msg}")
                                    st.error(error_msg)
                                    continue
                                finally:
                                    # Clean up local temp files
                                    if os.path.exists(clip_path):
                                        os.remove(clip_path)
                                    if os.path.exists(temp_original):
                                        os.remove(temp_original)
                            
                            # Display metadata
                            current_model = st.session_state.get('embedding_model', 'TwelveLabs Marengo')
                            st.write(f"**Timestamp:** {metadata['timestamp']}")
                            st.write(f"**Video:** {metadata['video_id']}")
                            st.write(f"**Start:** {start_sec}s | **Duration:** {optimal_duration:.1f}s")
                            st.write(f"**Similarity:** {similarity_score:.4f}")
                            st.write(f"**Model:** {current_model}")
                            
                            # Show original clip
                            st.write("**Original Clip:**")
                            # Use the already generated presigned URL
                            st.video(original_clip_url)
                            
                            # Rating system - positioned between original and processed clips
                            # Check if this video has already been rated in this session
                            rating_key = f"rated_{metadata['video_id']}_{metadata['segment_id']}"
                            already_rated = st.session_state.get(rating_key, False)
                            
                            if already_rated:
                                # Show that rating was already submitted
                                submitted_rating = st.session_state.get(f"submitted_rating_{metadata['video_id']}_{metadata['segment_id']}", "Unknown")
                                st.success(f"✅ Already rated: {submitted_rating}/5")
                            else:
                                # Create a container for the rating section to minimize refresh impact
                                rating_container = st.container()
                                
                                with rating_container:
                                    st.write("**Rate this search result:**")
                                    
                                    # Use a form to minimize refreshes
                                    with st.form(key=f"rating_form_{idx}"):
                                        # Use selectbox instead of radio to avoid immediate refresh
                                        rating = st.selectbox(
                                            "Quality (1=Poor, 5=Excellent)",
                                            options=[None, 1, 2, 3, 4, 5],
                                            index=0,  # Default to None
                                            key=f"rating_select_{idx}",
                                            help="Select how well this clip matches your search query"
                                        )
                                        
                                        # Submit button
                                        submitted = st.form_submit_button("Submit Rating", type="primary")
                                        
                                        if submitted and rating is not None:
                                            # Save rating to S3
                                            rating_data = {
                                                'timestamp': datetime.utcnow().isoformat(),
                                                'query': query,
                                                'query_hash': hashlib.md5(query.encode()).hexdigest()[:8],
                                                'model_type': current_model,
                                                'video_id': metadata['video_id'],
                                                'segment_id': metadata['segment_id'],
                                                'similarity_score': similarity_score,
                                                'rating': rating,
                                                'user_session': st.session_state.get('session_id', 'anonymous')
                                            }
                                            
                                            if save_rating_to_s3(rating_data):
                                                # Mark as rated and store the rating
                                                st.session_state[rating_key] = True
                                                st.session_state[f"submitted_rating_{metadata['video_id']}_{metadata['segment_id']}"] = rating
                                                st.success(f"✅ Rating {rating}/5 saved!")
                                                st.rerun()
                                            else:
                                                st.error("❌ Failed to save rating")
                                        elif submitted and rating is None:
                                            st.warning("👆 Please select a rating before submitting")
                            
                            # Process video if options are enabled
                            processed_clip_url = None
                            processing_applied = []
                            
                            if enable_labeling or enable_pii_blur:
                                # Check if we have cached processed clip
                                if s3_object_exists(processed_clip_key):
                                    print(f"🎯 Using cached processed clip from S3")
                                    try:
                                        processed_clip_url = get_s3_presigned_url(processed_clip_key)
                                        # Verify URL is valid (file exists and is accessible)
                                        if processed_clip_url:
                                            if enable_labeling:
                                                processing_applied.append("🏷️ Object Labels")
                                            if enable_pii_blur:
                                                processing_applied.append("🔒 PII Blurred")
                                        else:
                                            print(f"⚠️ Processed clip URL invalid, falling back to original")
                                            processed_clip_url = None
                                    except Exception as e:
                                        print(f"⚠️ Error getting processed clip URL: {e}")
                                        processed_clip_url = None
                                else:
                                    # Generate processed clip synchronously with progress
                                    print(f"🔄 Generating processed clip for S3 cache")
                                    processed_clip_path = f'/tmp/processed-{metadata["segment_id"]}-{idx}.mp4'
                                    
                                    # Create progress bar
                                    progress_bar = st.progress(0)
                                    status_text = st.empty()
                                    
                                    def update_progress(progress, message):
                                        progress_bar.progress(progress / 100)
                                        status_text.text(message)
                                    
                                    with st.spinner("Processing video..."):
                                        # Download original clip for processing
                                        temp_clip = f'/tmp/temp-clip-{metadata["segment_id"]}-{idx}.mp4'
                                        s3.download_file(S3_BUCKET, original_clip_key, temp_clip)
                                        
                                        # Use selected detection engine
                                        if "YOLOv8" in detection_engine:
                                            success = process_video_clip_yolov8(
                                                input_path=temp_clip,
                                                output_path=processed_clip_path,
                                                enable_labeling=enable_labeling,
                                                enable_pii_blur=enable_pii_blur,
                                                confidence_threshold=confidence_threshold if enable_labeling else 0.5,
                                                blur_kernel=blur_intensity if enable_pii_blur else 35,
                                                progress_callback=update_progress,
                                                blur_faces=blur_faces if enable_pii_blur else False,
                                                blur_plates=blur_plates if enable_pii_blur else False,
                                                enable_tracking=enable_tracking
                                            )
                                        else:
                                            success = process_video_clip_rekognition(
                                                input_path=temp_clip,
                                                output_path=processed_clip_path,
                                                enable_labeling=enable_labeling,
                                                enable_pii_blur=enable_pii_blur,
                                                confidence_threshold=confidence_threshold if enable_labeling else 0.7,
                                                blur_kernel=blur_intensity if enable_pii_blur else 35,
                                                progress_callback=update_progress
                                            )
                                    
                                    # Clear progress indicators
                                    progress_bar.empty()
                                    status_text.empty()
                                    
                                    if success and os.path.exists(processed_clip_path):
                                        # Upload processed clip to S3
                                        upload_video_to_s3(processed_clip_path, processed_clip_key)
                                        
                                        # Verify S3 upload completed successfully
                                        if s3_object_exists(processed_clip_key):
                                            processed_clip_url = get_s3_presigned_url(processed_clip_key)
                                            print(f"✅ Processed video ready in S3: {processed_clip_key}")
                                            
                                            if enable_labeling:
                                                processing_applied.append("🏷️ Object Labels")
                                            if enable_pii_blur:
                                                processing_applied.append("🔒 PII Blurred")
                                        else:
                                            print(f"❌ S3 upload failed for: {processed_clip_key}")
                                            processed_clip_url = None
                                        
                                        # Clean up local files
                                        if os.path.exists(processed_clip_path):
                                            os.remove(processed_clip_path)
                                        if os.path.exists(temp_clip):
                                            os.remove(temp_clip)
                                    else:
                                        st.warning("⚠️ Video processing failed - showing original video")
                                        print(f"⚠️ Processing failed, falling back to original clip")
                                        processed_clip_url = None
                            
                            # Display video player - use processed if available, otherwise original
                            video_url = processed_clip_url if processed_clip_url else original_clip_url
                            
                            # Verify video URL is valid before displaying
                            if not video_url:
                                st.error("❌ Video URL not available")
                                print(f"❌ No valid video URL for clip {idx}")
                                continue
                            else:
                                st.write("**Processed Clip:**")
                                # Use the already generated presigned URL
                                st.video(video_url)
                                st.write(f"**Processing Applied:** {', '.join(processing_applied)}")
                                if processed_clip_url is None:
                                    st.write("⚠️ Video processing failed - showing original video")
                        
                                    st.warning("⚠️ Processed video not available")
                                
                                # Display video summary if available
                                video_id = metadata.get('video_id')
                                if video_id:
                                    summary_data = get_video_summary(video_id)
                                    if summary_data:
                                        with st.expander("📋 AI Safety Summary", expanded=False):
                                            st.write(summary_data.get('summary', 'No summary available'))
                                            if 'model' in summary_data:
                                                st.caption(f"Model: {summary_data['model']}")
                                
                        except subprocess.CalledProcessError as e:
                            st.error(f"FFmpeg error: {e.stderr}")
                        except Exception as e:
                            st.error(f"Error processing clip: {e}")
                else:
                    st.write('No matching clips found.')
            except Exception as e:
                st.error(f"Search error: {str(e)}")
            finally:
                # Reset search state
                st.session_state['search_running'] = False

# Tab 2: Uploaded Videos
with main_tabs[1]:
    st.subheader('Uploaded Videos')

    # Action Buttons
    col1, col2, col3 = st.columns([1, 1, 2])
    with col1:
        if st.button('🔄 Check Now'):
            updated, progress_data = check_indexing_status()
            if updated > 0:
                st.success(f'🎉 Updated {updated} job(s)! Refreshing page...')
                time.sleep(1)
                st.rerun()
            else:
                st.info('No status updates found. Jobs may still be processing.')

    with col2:
        if st.button('🗑️ Clear Vector DB', type="secondary", help="Clear all embeddings from ChromaDB (useful for testing)"):
            # Add confirmation
            st.warning("⚠️ This will delete ALL embeddings from the vector database!")
            if st.button("✅ Confirm Clear", type="primary", key="confirm_clear"):
                try:
                    # Delete the collection and recreate it
                    chroma_client.delete_collection(name=CHROMA_COLLECTION)
                    # Recreate the collection and update global reference
                    chroma_collection = chroma_client.create_collection(
                        name=CHROMA_COLLECTION,
                        metadata={"hnsw:space": "cosine"}
                    )
                    st.success("✅ Vector database cleared successfully!")
                    st.rerun()
                except Exception as e:
                    st.error(f"❌ Failed to clear vector database: {e}")

    with col3:
        auto_refresh = st.checkbox('Auto-refresh status (every 10s)', value=False)
        if auto_refresh:
            updated, progress_data = check_indexing_status()
            if updated > 0:
                st.success(f'🎉 Auto-refresh: Updated {updated} job(s)!')
                st.rerun()
            else:
                st.info("✅ All jobs completed - auto-refresh disabled")

    # Show progress bars for indexing jobs
    if st.session_state["indexing_jobs"]:
        st.subheader("📊 Indexing Progress")
        updated, progress_data = check_indexing_status()
        
        for video_id, job_status in st.session_state["indexing_jobs"].items():
            if job_status in ["Indexing...", "Waiting", "Processing"]:
                progress_info = progress_data.get(video_id, {})
                embeddings_count = progress_info.get('embeddings_found', 0)
                
                if embeddings_count > 0:
                    # Show progress bar based on embeddings found
                    progress_percent = min(embeddings_count / 100, 1.0)  # Assume ~100 embeddings for completion
                    st.progress(progress_percent)
                    st.write(f"**{video_id}**: {embeddings_count} embeddings generated")
                else:
                    st.write(f"**{video_id}**: Waiting for Bedrock to generate embeddings...")
                    st.progress(0.1)  # Small progress to show it's working

    # List uploaded videos
    try:
        videos = s3.list_objects_v2(Bucket=S3_BUCKET, Prefix='videos/')
        if videos.get('Contents'):
            video_list = []
            for obj in videos['Contents']:
                key = obj['Key']
                filename = key.split('/')[-1]
                video_id = generate_video_id(filename)
                
                # Check if embeddings exist (both Titan and Marengo)
                embeddings_exists = False
                titan_embeddings_exists = False
                marengo_embeddings_exists = False
                
                try:
                    prefixes_to_try = [
                        f'embeddings-output/{video_id}/',
                        f'embeddings-output/{video_id}.mp4/',
                    ]
                    
                    for prefix in prefixes_to_try:
                        emb_obj = s3.list_objects_v2(Bucket=S3_BUCKET, Prefix=prefix)
                        for eobj in emb_obj.get('Contents', []):
                            k = eobj['Key']
                            if k.startswith(prefix):
                                if k.endswith('output.json'):
                                    marengo_embeddings_exists = True
                                    embeddings_exists = True
                                elif k.endswith('titan_embeddings.json'):
                                    titan_embeddings_exists = True
                                    embeddings_exists = True
                except Exception:
                    embeddings_exists = False

                # Check which models have been used for indexing
                indexed_models = get_video_indexed_models(video_id)
                available_models, _ = get_available_models_for_video(video_id)
                
                # Determine status
                job_status = st.session_state["indexing_jobs"].get(video_id, "")
                
                if indexed_models:
                    if len(indexed_models) == 1:
                        status_icon = "✅"
                        status_text = f"Indexed ({indexed_models[0]})"
                    else:
                        status_icon = "✅"
                        status_text = f"Indexed ({', '.join(indexed_models)})"
                elif job_status in ["Indexing...", "Processing", "Waiting", "Processing with Titan..."]:
                    status_icon = "🔄"
                    status_text = "Indexing..."
                elif job_status.startswith("Failed"):
                    status_icon = "❌"
                    status_text = "Failed"
                elif embeddings_exists and not indexed_models:
                    status_icon = "⚠️"
                    status_text = "Processing"
                else:
                    status_icon = "⏳"
                    status_text = "Pending"

                status_label = f"{status_icon} {status_text}"
                video_list.append({
                    'key': key, 
                    'video_id': video_id, 
                    'filename': filename, 
                    'indexed': status_label,
                    'indexed_models': indexed_models,
                    'available_models': available_models
                })

            if video_list:
                # Display in grid
                cols = st.columns(2)
                for i, video in enumerate(video_list):
                    with cols[i % 2]:
                        st.write(f"**{video['filename']}**")
                        st.write(f"Status: {video['indexed']}")
                        st.write(f"ID: {video['video_id']}")
                        
                        # Multi-model indexing section
                        st.write("**🤖 Model Indexing:**")
                        
                        # Show indexed models
                        if video['indexed_models']:
                            st.write(f"✅ **Indexed with:** {', '.join(video['indexed_models'])}")
                        
                        # Show available models for indexing
                        if video['available_models']:
                            st.write(f"⏳ **Available models:** {', '.join(video['available_models'])}")
                            
                            # Model selection and indexing trigger
                            with st.form(key=f"indexing_form_{video['video_id']}"):
                                selected_model = st.selectbox(
                                    f"Select model to index {video['filename']}:",
                                    options=video['available_models'],
                                    key=f"model_select_{video['video_id']}"
                                )
                                
                                if st.form_submit_button(f"🚀 Index with {selected_model.split()[0]}", type="primary"):
                                    with st.spinner(f'Starting indexing for {video["filename"]} with {selected_model}...'):
                                        success = index_video(video['video_id'], video['key'], selected_model)
                                        if success:
                                            st.success(f'Indexing started for {video["filename"]} with {selected_model}!')
                                            st.rerun()
                                        else:
                                            st.error(f'Failed to start indexing for {video["filename"]}')
                        else:
                            st.info("🎉 All models have been used for indexing this video!")
                        
                        # Add video playback
                        try:
                            presigned_url = s3.generate_presigned_url(
                                'get_object', 
                                Params={'Bucket': S3_BUCKET, 'Key': video['key'], 'ResponseContentType': 'video/mp4'}, 
                                ExpiresIn=3600
                            )
                            st.video(presigned_url)
                        except Exception as e:
                            st.error(f"Error loading video: {e}")
                        
                        # Display video summary if available
                        summary_data = get_video_summary(video['video_id'])
                        if summary_data:
                            with st.expander("📋 AI Safety Summary (Nova Premier)", expanded=False):
                                st.markdown("### Safety Analysis")
                                st.write(summary_data.get('summary', 'No summary available'))
                                
                                # Show metadata
                                if 'timestamp' in summary_data:
                                    st.caption(f"Generated: {summary_data['timestamp']}")
                                if 'model' in summary_data:
                                    st.caption(f"Model: {summary_data['model']}")
                        
                        st.write("---")
            else:
                st.write("No videos uploaded yet.")
        else:
            st.write("No videos uploaded yet.")

    except Exception as e:
        st.error(f"Error listing videos: {e}")

# Tab 3: Add New Video
with main_tabs[2]:
    st.subheader("Add New Video")
    
    # Sub-tabs for upload methods
    upload_tabs = st.tabs(["Upload from file", "Download from YouTube"])
    
    with upload_tabs[0]:
        st.info("📁 **File Upload**: Videos up to 2GB are supported. Larger files may take longer to process.")
        uploaded_file = st.file_uploader(
            "Choose a video file", 
            type=['mp4', 'mov', 'avi', 'mkv'],
            help="Upload videos up to 2GB in size. Supported formats: MP4, MOV, AVI, MKV"
        )
        if uploaded_file is not None:
            filename = uploaded_file.name
            # Sanitize filename to handle long names and special characters
            clean_filename = sanitize_s3_key(filename.replace(' ', '_'))
            video_id = generate_video_id(clean_filename)
            s3_key = f'videos/{clean_filename}'
            
            # Show current model selection
            current_model = st.session_state.get('embedding_model', 'TwelveLabs Marengo')
            st.info(f"📋 Video will be indexed using: **{current_model}**")
            
            # DEBUG: Log model selection in UI
            print(f"🔍 DEBUG: Upload UI - current_model='{current_model}'")
            print(f"🔍 DEBUG: Upload UI - session_state embedding_model='{st.session_state.get('embedding_model', 'NOT_SET')}'")
            
            if st.button('Upload & Index Video'):
                with st.spinner('Uploading video...'):
                    # Upload to S3
                    s3.upload_fileobj(uploaded_file, S3_BUCKET, s3_key)
                    video_uri = f"s3://{S3_BUCKET}/{s3_key}"
                    
                    st.session_state["upload_messages"].append({
                        'type': 'success',
                        'message': f'Uploaded: {video_uri}',
                        'video_id': video_id
                    })
                    
                    # DEBUG: Log before calling index_video
                    print(f"🔍 DEBUG: About to call index_video with model='{current_model}'")
                    
                    # Start indexing with selected model
                    success = index_video(video_id, s3_key, current_model)
                    if success:
                        st.session_state["indexing_messages"].append({
                            'type': 'success',
                            'message': f'🚀 Indexing started with {current_model}! Go to "📁 Uploaded Videos" tab and click "🔄 Check Now" to monitor progress.',
                            'video_id': video_id
                        })
                        st.success(f"✅ Indexing job started! Video ID: {video_id}")
                        st.rerun()

    with upload_tabs[1]:
        # Clear YouTube URL after successful upload
        if 'clear_youtube_url' not in st.session_state:
            st.session_state['clear_youtube_url'] = False
        
        if st.session_state['clear_youtube_url']:
            youtube_url = ""
            st.session_state['clear_youtube_url'] = False
        else:
            youtube_url = st.text_input('Enter YouTube URL')
        
        # Show current model selection for YouTube videos
        if youtube_url:
            current_model = st.session_state.get('embedding_model', 'TwelveLabs Marengo')
            st.info(f"📋 YouTube video will be indexed using: **{current_model}**")
        
        if youtube_url and st.button('Download & Index Video'):
            with st.spinner('Downloading video...'):
                try:
                    # Optimized download strategy for speed
                    download_strategies = [
                        # Strategy 1: Fast download with lower quality
                        {
                            'outtmpl': '/tmp/%(title)s.%(ext)s',
                            'format': 'best[height<=480][ext=mp4]',  # 480p MP4 for speed
                            'concurrent_fragment_downloads': 4,  # Parallel downloads
                            'http_chunk_size': 10485760,  # 10MB chunks
                            'retries': 2,
                            'fragment_retries': 2,
                            'no_check_certificate': True,
                            'ignoreerrors': True,
                            'quiet': False,
                            'no_warnings': False,
                        },
                        # Strategy 2: Even faster with lower quality
                        {
                            'outtmpl': '/tmp/%(title)s.%(ext)s',
                            'format': 'worst[ext=mp4]',  # Lowest quality for max speed
                            'concurrent_fragment_downloads': 4,
                            'retries': 1,
                            'no_check_certificate': True,
                            'ignoreerrors': True,
                        }
                    ]
                    
                    info = None
                    filename = None
                    
                    for i, ydl_opts in enumerate(download_strategies):
                        try:
                            st.info(f"Trying download strategy {i+1}...")
                            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                                info = ydl.extract_info(youtube_url, download=True)
                                filename = ydl.prepare_filename(info)
                                if os.path.exists(filename):
                                    st.success(f"Download successful with strategy {i+1}")
                                    break
                        except Exception as strategy_error:
                            st.warning(f"Strategy {i+1} failed: {str(strategy_error)}")
                            continue
                    
                    if not info or not filename or not os.path.exists(filename):
                        raise Exception("All download strategies failed")
                    
                    if os.path.exists(filename):
                        # Sanitize filename to handle long names and special characters
                        clean_filename = sanitize_s3_key(os.path.basename(filename).replace(' ', '_'))
                        video_id = generate_video_id(clean_filename)
                        s3_key = f'videos/{clean_filename}'
                        
                        # Upload to S3 with progress
                        st.info(f"📤 Uploading to S3: {clean_filename}")
                        s3.upload_file(filename, S3_BUCKET, s3_key)
                        video_uri = f"s3://{S3_BUCKET}/{s3_key}"
                        st.success(f"✅ Upload complete!")
                        
                        st.session_state["upload_messages"].append({
                            'type': 'success',
                            'message': f'Uploaded: {video_uri}',
                            'video_id': video_id
                        })
                        
                        # Start indexing with selected model
                        with st.spinner('Starting indexing job...'):
                            success = index_video(video_id, s3_key, current_model)
                            if success:
                                st.session_state["indexing_messages"].append({
                                    'type': 'success',
                                    'message': f'🚀 Indexing started with {current_model}! Go to "📁 Uploaded Videos" tab and click "🔄 Check Now" to monitor progress.',
                                    'video_id': video_id
                                })
                                st.success(f"✅ Indexing job started! Video ID: {video_id}")
                            else:
                                st.error(f"❌ Failed to start indexing job for {video_id}")
                        
                        # Clean up local file
                        os.remove(filename)
                        
                        # Set flag to clear YouTube URL on next render
                        st.session_state['clear_youtube_url'] = True
                        
                        st.rerun()
                except Exception as e:
                    error_msg = str(e)
                    if "Sign in to confirm you're not a bot" in error_msg:
                        st.error("🔐 YouTube authentication required. Please update your cookies.txt file with fresh YouTube cookies.")
                        st.info("💡 To fix this: 1) Export cookies from your browser, 2) Replace cookies.txt, 3) Try again")
                    else:
                        st.error(f"Download error: {error_msg}")

    # Display Status Messages Section
    if st.session_state["upload_messages"] or st.session_state["indexing_messages"]:
        st.subheader("📋 Status Messages")
        for msg in st.session_state["upload_messages"] + st.session_state["indexing_messages"]:
            if msg['type'] == 'success':
                st.success(msg['message'])
            elif msg['type'] == 'info':
                st.info(msg['message'])
            elif msg['type'] == 'warning':
                st.warning(msg['message'])
            elif msg['type'] == 'error':
                st.error(msg['message'])

# Tab 4: Model Analytics
with main_tabs[3]:
    st.subheader('📊 Model Analytics')
    st.write("Compare the performance of different embedding models based on user ratings.")
    
    # Load analytics data
    analytics = get_model_analytics()
    
    if analytics is None:
        st.info("📊 No rating data available yet. Start rating search results to see analytics!")
        st.write("**How to contribute:**")
        st.write("1. Search for videos using different models")
        st.write("2. Rate the search results (1-5 stars)")
        st.write("3. View analytics here to compare model performance")
    else:
        df = analytics['dataframe']
        model_stats = analytics['model_stats']
        total_ratings = analytics['total_ratings']
        
        # Summary statistics
        st.metric("Total Ratings", total_ratings)
        
        # Model comparison
        st.subheader("Model Performance Comparison")
        
        # Create comparison chart
        model_avg_ratings = df.groupby('model_type')['rating'].mean().round(2)
        model_counts = df.groupby('model_type')['rating'].count()
        
        # Bar chart
        fig = px.bar(
            x=model_avg_ratings.index,
            y=model_avg_ratings.values,
            title="Average Rating by Model",
            labels={'x': 'Model', 'y': 'Average Rating'},
            color=model_avg_ratings.values,
            color_continuous_scale='RdYlGn'
        )
        fig.update_layout(showlegend=False)
        st.plotly_chart(fig, width=True)
        
        # Detailed statistics
        col1, col2 = st.columns(2)
        
        with col1:
            st.subheader("📈 Rating Distribution")
            rating_counts = df['rating'].value_counts().sort_index()
            fig2 = px.bar(
                x=rating_counts.index,
                y=rating_counts.values,
                title="Rating Distribution",
                labels={'x': 'Rating', 'y': 'Count'}
            )
            st.plotly_chart(fig2, width=True)
        
        with col2:
            st.subheader("🎯 Model Statistics")
            for model in model_avg_ratings.index:
                model_data = df[df['model_type'] == model]
                avg_rating = model_data['rating'].mean()
                count = len(model_data)
                avg_similarity = model_data['similarity_score'].mean()
                
                st.write(f"**{model}**")
                st.write(f"• Average Rating: {avg_rating:.2f}/5")
                st.write(f"• Total Ratings: {count}")
                st.write(f"• Avg Similarity: {avg_similarity:.3f}")
                st.write("---")
        
        # Raw data table
        with st.expander("📋 Raw Rating Data", expanded=False):
            st.dataframe(df[['timestamp', 'model_type', 'rating', 'similarity_score', 'query']], width=True)
