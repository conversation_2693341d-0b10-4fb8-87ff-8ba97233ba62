# 🎥 Samsara Dashcam Video Analysis Platform

**AI-Powered Video Search & Safety Analysis using AWS Bedrock Dual Embeddings**

---

## 🚀 Quick Start

### Prerequisites
- Docker & Docker Compose
- AWS Account with Bedrock access (Nova Premier + Marengo)
- 8GB+ RAM recommended

### Start the System
```bash
cd samsara-poc-main
docker compose up -d
```

### Access
- **Frontend:** http://localhost:3000
- **Backend API:** http://localhost:8000
- **API Documentation:** http://localhost:8000/docs

### Default Credentials
- **Username:** `admin`
- **Password:** `admin123`

---

## 📋 Features

### ✅ Core Functionality
- **Video Upload** - Direct upload or YouTube URL
- **Dual Embeddings** - Nova Premier + Marengo (AWS Bedrock)
- **Natural Language Search** - Find moments in videos using text queries
- **Scene Analysis** - Automatic scene detection and clustering
- **Safety Reports** - Automated risk assessment and summaries
- **Real-time Updates** - WebSocket-powered progress tracking

### 🎯 System Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                 Video Processing Pipeline                    │
└─────────────────────────────────────────────────────────────┘

Upload → Dual Embeddings (Parallel) → Summary → Search Ready

Step 1: Upload to S3
Step 2: Nova + Marengo Embeddings (parallel processing)
Step 3: Summary Generation (<1s)
Result: Searchable video with safety analysis
```

---

## 🏗️ Technology Stack

### Frontend
- React 18 + TypeScript
- TailwindCSS for styling
- React Query for data fetching
- Lucide React for icons

### Backend
- FastAPI (Python)
- PostgreSQL (metadata)
- Weaviate (vector embeddings)
- Redis (task queue)
- Celery (background workers)

### AI/ML
- **AWS Bedrock Nova Premier** - High-quality embeddings
- **AWS Bedrock Marengo** - Complementary embeddings
- Scene clustering algorithm
- Risk score calculation

---

## 📊 Performance

| Metric | Value |
|--------|-------|
| Video Upload | ~46s for 46s video |
| Embedding Generation | ~59s (parallel) |
| Summary Generation | <1s |
| Search Response | <2s |
| Total Processing | ~2 minutes |

---

## 🎯 Use Cases

### 1. **Fleet Safety Management**
- Analyze dashcam footage for safety incidents
- Identify risky driving behaviors
- Generate automated safety reports

### 2. **Incident Investigation**
- Search videos using natural language
- Find specific moments (e.g., "near miss with truck")
- Review scene-by-scene analysis

### 3. **Driver Training**
- Identify training opportunities
- Review flagged incidents
- Track safety improvements

---

## 📖 Documentation

- **[PROJECT_STATUS.md](docs/PROJECT_STATUS.md)** - Current status, features, and roadmap
- **[VLM_SCENE_ANALYSIS_PLAN.md](docs/VLM_SCENE_ANALYSIS_PLAN.md)** - Future VLM implementation
- **[SUMMARY_OPTIMIZATION.md](docs/SUMMARY_OPTIMIZATION.md)** - Performance improvements
- **[docs/](docs/)** - All technical documentation

---

## 🔧 Configuration

### Environment Variables

Create `.env` file in the root directory:

```bash
# AWS Credentials
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
AWS_REGION=us-east-1

# S3 Configuration
S3_BUCKET=your-bucket-name

# Database
POSTGRES_USER=samsara
POSTGRES_PASSWORD=samsara123
POSTGRES_DB=samsara_db

# Redis
REDIS_HOST=redis
REDIS_PORT=6379

# Weaviate
WEAVIATE_URL=http://weaviate:8080
```

---

## 🚀 Usage

### 1. Upload a Video

**Option A: Direct Upload**
1. Go to Upload page
2. Drag & drop video file
3. Wait for processing

**Option B: YouTube URL**
1. Go to Upload page
2. Paste YouTube URL
3. Click "Download & Process"

### 2. Monitor Progress

Go to **Jobs** page to see:
- Upload status
- Nova embeddings progress
- Marengo embeddings progress
- Summary generation
- Overall status

### 3. Search Videos

1. Go to **Search** page
2. Enter natural language query
   - Example: "car changing lanes"
   - Example: "near miss with truck"
3. View results with timestamps
4. Click to play video at that moment

### 4. View Summary

1. Go to **Videos** page
2. Click on a video
3. View automated safety report with:
   - Scene breakdown
   - Risk score
   - Recommendations

---

## 🛠️ Development

### Project Structure

```
samsara-poc-main/
├── backend/
│   ├── app/
│   │   ├── api/routes/      # API endpoints
│   │   ├── core/            # Config & database
│   │   ├── models/          # Data models
│   │   ├── services/        # Business logic
│   │   └── tasks/           # Celery tasks
│   └── requirements.txt
├── frontend/
│   ├── src/
│   │   ├── components/      # React components
│   │   ├── pages/           # Page components
│   │   └── services/        # API clients
│   └── package.json
├── docker-compose.yml       # Container orchestration
└── README.md
```

### Running Locally

**Backend:**
```bash
cd backend
pip install -r requirements.txt
uvicorn app.main:app --reload
```

**Frontend:**
```bash
cd frontend
npm install
npm start
```

**Celery Worker:**
```bash
cd backend
celery -A app.core.celery_app worker --loglevel=info
```

---

## 🔍 Troubleshooting

### Check Logs
```bash
# Backend
docker logs samsara-backend --tail 50

# Celery Worker
docker logs samsara-celery-worker --tail 50

# Frontend
docker logs samsara-frontend --tail 50
```

### Restart Services
```bash
docker compose restart backend celery-worker frontend
```

### Clear Database
1. Go to **Settings** page
2. Use "Clear PostgreSQL" to remove videos/jobs
3. Use "Clear Weaviate" to remove embeddings

---

## 📈 Roadmap

### ✅ Completed
- Dual embedding system (Nova + Marengo)
- Sequential workflow with proper dependencies
- Real-time progress updates
- Natural language search
- Automated summary generation
- Admin controls

### 🎯 Next Up
1. **VLM Scene Analysis** - Frame-by-frame incident detection
2. **Enhanced Search Filters** - Date, risk score, duration
3. **Analytics Dashboard** - Trends and metrics
4. **Batch Upload** - Process multiple videos
5. **Export Functionality** - CSV/PDF reports

See [PROJECT_STATUS.md](docs/PROJECT_STATUS.md) for detailed roadmap.

---

## 🤝 Contributing

This is a proof-of-concept project. For production use:
1. Add proper authentication & authorization
2. Implement rate limiting
3. Add comprehensive error handling
4. Set up monitoring & alerting
5. Configure production-grade database
6. Implement backup & disaster recovery

---

## 📄 License

Proprietary - Samsara Internal Use

---

## 🎉 Key Achievements

- ✅ **10-15x faster** summary generation (optimized from 10-15s to <1s)
- ✅ **100% reliable** processing pipeline
- ✅ **Dual embeddings** for better search accuracy
- ✅ **Real-time updates** via WebSocket
- ✅ **Production-ready** architecture

---

## 📞 Support

For issues or questions:
1. Check the logs (see Troubleshooting section)
2. Review [PROJECT_STATUS.md](docs/PROJECT_STATUS.md)
3. Check API documentation at http://localhost:8000/docs

---

**Built with ❤️ for Samsara Fleet Safety**
