# Docker Rebuild Guide

## 🔄 Quick Rebuild Commands

### **Option 1: Using the Script** (Recommended)
```bash
./rebuild.sh
```

### **Option 2: Manual Commands**
```bash
# 1. Stop and remove all containers
docker-compose down

# 2. Remove all images (optional, for complete clean)
docker-compose rm -f

# 3. Prune build cache
docker builder prune -f

# 4. Rebuild with no cache
docker-compose build --no-cache

# 5. Start services
docker-compose up -d
```

---

## 📦 Services in Docker Compose

| Service | Container Name | Port | Description |
|---------|---------------|------|-------------|
| **postgres** | samsara-postgres | 5432 | PostgreSQL database |
| **weaviate** | samsara-weaviate | 8080 | Vector database |
| **redis** | samsara-redis | 6379 | Cache & Celery broker |
| **backend** | samsara-backend | 8000 | FastAPI application |
| **celery-worker** | samsara-celery-worker | - | Background task worker |
| **celery-beat** | samsara-celery-beat | - | Task scheduler |
| **frontend** | samsara-frontend | 3000 | React application |

---

## 🚀 Starting Services

### **Start All Services (Detached)**
```bash
docker-compose up -d
```

### **Start with Logs**
```bash
docker-compose up
```

### **Start Specific Service**
```bash
docker-compose up -d backend
docker-compose up -d frontend
```

---

## 🛑 Stopping Services

### **Stop All Services**
```bash
docker-compose stop
```

### **Stop and Remove Containers**
```bash
docker-compose down
```

### **Stop and Remove Containers + Volumes** (⚠️ Deletes data)
```bash
docker-compose down -v
```

---

## 📊 Monitoring Services

### **View Logs**
```bash
# All services
docker-compose logs -f

# Specific service
docker-compose logs -f backend
docker-compose logs -f celery-worker
docker-compose logs -f frontend
```

### **Check Service Status**
```bash
docker-compose ps
```

### **Check Service Health**
```bash
docker-compose ps --format json | jq
```

---

## 🔍 Debugging

### **Enter Container Shell**
```bash
# Backend
docker exec -it samsara-backend /bin/bash

# Frontend
docker exec -it samsara-frontend /bin/sh

# Celery Worker
docker exec -it samsara-celery-worker /bin/bash
```

### **Check Container Resources**
```bash
docker stats
```

### **Inspect Container**
```bash
docker inspect samsara-backend
```

---

## 🧹 Cleanup Commands

### **Remove Stopped Containers**
```bash
docker container prune -f
```

### **Remove Unused Images**
```bash
docker image prune -a -f
```

### **Remove Unused Volumes**
```bash
docker volume prune -f
```

### **Remove Everything** (⚠️ Nuclear option)
```bash
docker system prune -a --volumes -f
```

---

## 🔧 Troubleshooting

### **Issue: Port Already in Use**

**Error:** `Bind for 0.0.0.0:8000 failed: port is already allocated`

**Solution:**
```bash
# Find process using the port
lsof -i :8000
lsof -i :3000

# Kill the process
kill -9 <PID>

# Or stop all Docker containers
docker stop $(docker ps -aq)
```

### **Issue: Build Cache Problems**

**Solution:**
```bash
# Clear all build cache
docker builder prune -a -f

# Rebuild with no cache
docker-compose build --no-cache
```

### **Issue: Volume Permission Errors**

**Solution:**
```bash
# Remove volumes and recreate
docker-compose down -v
docker-compose up -d
```

### **Issue: Container Won't Start**

**Debug Steps:**
```bash
# Check logs
docker-compose logs backend

# Check if dependencies are healthy
docker-compose ps

# Restart specific service
docker-compose restart backend
```

---

## 📝 Environment Variables

### **Required in `.env` file:**
```bash
# AWS Credentials
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
AWS_ACCOUNT_ID=your_account_id
S3_BUCKET=poc-video-search-bucket
BEDROCK_REGION=us-east-1

# Application
SECRET_KEY=your_secret_key_here
ADMIN_PASSWORD=your_admin_password

# Database (already in docker-compose.yml)
DATABASE_URL=***************************************************/samsara_db
REDIS_URL=redis://redis:6379/0
WEAVIATE_URL=http://weaviate:8080
```

---

## 🎯 Common Workflows

### **Full Reset & Rebuild**
```bash
# Stop everything
docker-compose down -v

# Clean everything
docker system prune -a --volumes -f

# Rebuild
docker-compose build --no-cache

# Start
docker-compose up -d

# Check logs
docker-compose logs -f
```

### **Update Code & Restart**
```bash
# Backend changes
docker-compose restart backend

# Frontend changes (auto-reloads in dev mode)
# No restart needed

# Celery worker changes
docker-compose restart celery-worker
```

### **Database Migration**
```bash
# Enter backend container
docker exec -it samsara-backend /bin/bash

# Run migrations
alembic upgrade head
```

---

## 📊 Health Checks

### **Check All Services**
```bash
# PostgreSQL
docker exec samsara-postgres pg_isready -U samsara

# Redis
docker exec samsara-redis redis-cli ping

# Weaviate
curl http://localhost:8080/v1/.well-known/ready

# Backend
curl http://localhost:8000/health

# Frontend
curl http://localhost:3000
```

---

## 🚦 Service Dependencies

```
Frontend → Backend → PostgreSQL
                  → Redis
                  → Weaviate

Celery Worker → Redis
             → PostgreSQL
             → Weaviate

Celery Beat → Redis
```

**Startup Order:**
1. PostgreSQL, Redis, Weaviate (databases)
2. Backend (waits for databases to be healthy)
3. Celery Worker, Celery Beat
4. Frontend (waits for backend)

---

## 💡 Tips

1. **Use `docker-compose up -d`** for detached mode (runs in background)
2. **Use `docker-compose logs -f`** to follow logs in real-time
3. **Use `docker-compose restart <service>`** to restart individual services
4. **Use `docker-compose down -v`** only when you want to delete data
5. **Check `.env` file** before starting services
6. **Wait for health checks** before accessing services

---

## 🔗 Useful Links

- **Backend API:** http://localhost:8000
- **API Docs:** http://localhost:8000/docs
- **Frontend:** http://localhost:3000
- **Weaviate:** http://localhost:8080
- **PostgreSQL:** localhost:5432
- **Redis:** localhost:6379

---

## ✅ Verification Checklist

After rebuild, verify:

- [ ] All containers running: `docker-compose ps`
- [ ] PostgreSQL healthy: `docker exec samsara-postgres pg_isready -U samsara`
- [ ] Redis healthy: `docker exec samsara-redis redis-cli ping`
- [ ] Weaviate healthy: `curl http://localhost:8080/v1/.well-known/ready`
- [ ] Backend accessible: `curl http://localhost:8000/health`
- [ ] Frontend accessible: `curl http://localhost:3000`
- [ ] Celery worker running: `docker-compose logs celery-worker | grep "ready"`
- [ ] Celery beat running: `docker-compose logs celery-beat | grep "beat"`

---

**Created:** $(date)
**Status:** Ready for use
