# Celery Comprehensive Fix Plan

## 🐛 ISSUES IDENTIFIED

### **Critical Issues:**
1. ❌ **Progress bar shows 100% but upload still running**
   - Root Cause: Progress reaches 100% when triggering tasks, not when complete
   
2. ❌ **Missing timestamps**
   - Upload: Start time set, but end time not visible
   - Embedding: Start time set, end time missing
   - Object Detection: No timestamps at all

3. ❌ **Object Detection not triggered automatically**
   - Only triggered after summary completes
   - Should run in parallel with summary

4. ❌ **Upload is slow (not async)**
   - File upload happens synchronously in FastAPI endpoint
   - Should be async/chunked

5. ❌ **No parallel processing optimization**
   - Only 4 workers (default)
   - No task prioritization
   - No worker pools

### **Design Issues:**
6. ⚠️ **Progress tracking unreliable**
   - Can't calculate exact % for AWS Bedrock jobs
   - Better to show elapsed time

7. ⚠️ **Task dependencies not clear**
   - Upload → Embedding + Summary (parallel)
   - Summary → Object Detection (sequential)
   - Should be: Upload → All tasks in parallel

---

## 📋 COMPREHENSIVE FIX PLAN

### **Phase 1: Fix Progress & Timestamps** (2 hours)

#### **1.1 Remove Misleading Progress Bars**
- Replace progress % with elapsed time
- Show "Processing..." with timer
- Only show completion when actually done

#### **1.2 Fix All Timestamps**
- Upload: Already has start/end ✓
- Embedding: Add completion timestamp
- Object Detection: Add start/end timestamps
- Summary: Already has start/end ✓

#### **1.3 Fix Progress State**
- Don't set 100% until task actually completes
- Remove "Starting indexing tasks..." step

---

### **Phase 2: Optimize Upload** (3 hours)

#### **2.1 Async File Upload**
- Stream file to S3 directly (no temp file)
- Use multipart upload for large files
- Show upload progress in real-time

#### **2.2 Parallel Task Triggering**
- Trigger all tasks immediately after upload
- Don't wait for dependencies
- Use Celery chains/groups

---

### **Phase 3: Optimize Celery Workers** (2 hours)

#### **3.1 Increase Worker Concurrency**
```yaml
celery-worker:
  command: celery -A app.core.celery_app worker --loglevel=info --concurrency=16
```

#### **3.2 Add Worker Pools**
- Fast pool: Upload, metadata extraction
- Slow pool: Embedding, summary, detection
- Separate queues for different task types

#### **3.3 Task Prioritization**
- High priority: Upload
- Medium priority: Embedding, Summary
- Low priority: Object Detection

---

### **Phase 4: Fix Task Dependencies** (1 hour)

#### **4.1 Parallel Execution**
```python
# OLD (Sequential)
upload → embedding → summary → detection

# NEW (Parallel)
upload → [embedding, summary, detection] (all parallel)
```

#### **4.2 Remove Unnecessary Dependencies**
- Object Detection doesn't need summary to complete
- All can run immediately after upload

---

### **Phase 5: Frontend Improvements** (2 hours)

#### **5.1 Replace Progress Bars with Timers**
```tsx
// OLD
<ProgressBar percent={task.progress.percent} />

// NEW
<ElapsedTime startTime={task.started_at} />
```

#### **5.2 Better Status Display**
- Show "Uploading..." with elapsed time
- Show "Processing..." with elapsed time
- Show "Completed" with duration

---

## 🎯 IMPLEMENTATION DETAILS

### **Fix 1: Remove Misleading Progress**

**File:** `backend/app/tasks/video_tasks.py`

```python
# REMOVE THIS (lines 128-138)
# Step 5: Trigger processing tasks (100%)
self.update_state(
    state='PROGRESS',
    meta={
        'current': 5,
        'total': 5,
        'percent': 100,  # ← WRONG! Task not done yet
        'status': 'Starting indexing tasks...',
        'video_id': video_id
    }
)

# REPLACE WITH
# Don't set progress to 100% - let Celery mark as SUCCESS
logger.info(f"✅ Upload complete, triggering processing tasks")
```

---

### **Fix 2: Add Embedding Completion Timestamp**

**File:** `backend/app/tasks/embedding_tasks.py`

```python
# After indexing completes (around line 200)
video = self.db.query(Video).filter(Video.video_id == video_id).first()
if video:
    video.embedding_completed_at = datetime.utcnow()  # ← ADD THIS
    video.status = VideoStatus.INDEXED
    self.db.commit()
```

---

### **Fix 3: Trigger All Tasks in Parallel**

**File:** `backend/app/tasks/video_tasks.py`

```python
# CURRENT (Sequential)
start_embedding_generation.delay(video_id, s3_key, model_name)
generate_video_summary.delay(video_id, s3_key)
# Object detection triggered AFTER summary completes

# NEW (Parallel)
from celery import group

# Trigger all tasks in parallel
parallel_tasks = group(
    start_embedding_generation.s(video_id, s3_key, model_name),
    generate_video_summary.s(video_id, s3_key),
    detect_objects_in_video.s(video_id, s3_key)
)
parallel_tasks.apply_async()
```

---

### **Fix 4: Increase Worker Concurrency**

**File:** `docker-compose.yml`

```yaml
celery-worker:
  command: celery -A app.core.celery_app worker 
    --loglevel=info 
    --concurrency=16  # ← Increase from 4 to 16
    --max-tasks-per-child=10  # ← Prevent memory leaks
```

---

### **Fix 5: Add Task Queues**

**File:** `backend/app/core/celery_app.py`

```python
# Add task routing
celery_app.conf.task_routes = {
    'app.tasks.video_tasks.process_video_upload': {'queue': 'fast'},
    'app.tasks.embedding_tasks.*': {'queue': 'slow'},
    'app.tasks.video_tasks.generate_video_summary': {'queue': 'slow'},
    'app.tasks.video_tasks.detect_objects_in_video': {'queue': 'slow'},
}
```

**File:** `docker-compose.yml`

```yaml
# Fast worker for uploads
celery-worker-fast:
  command: celery -A app.core.celery_app worker 
    --loglevel=info 
    --concurrency=8
    --queues=fast

# Slow worker for processing
celery-worker-slow:
  command: celery -A app.core.celery_app worker 
    --loglevel=info 
    --concurrency=16
    --queues=slow
```

---

### **Fix 6: Frontend - Replace Progress with Timer**

**File:** `frontend/src/pages/JobsPage.tsx`

```tsx
// ADD: Elapsed time component
const ElapsedTime = ({ startTime }: { startTime: string }) => {
  const [elapsed, setElapsed] = useState(0);
  
  useEffect(() => {
    const interval = setInterval(() => {
      const start = new Date(startTime).getTime();
      const now = new Date().getTime();
      setElapsed(Math.floor((now - start) / 1000));
    }, 1000);
    
    return () => clearInterval(interval);
  }, [startTime]);
  
  const minutes = Math.floor(elapsed / 60);
  const seconds = elapsed % 60;
  
  return (
    <span className="text-sm text-gray-600">
      {minutes}:{seconds.toString().padStart(2, '0')} elapsed
    </span>
  );
};

// REPLACE progress bar with timer
{task.status === 'in_progress' && task.started_at && (
  <div className="flex items-center gap-2">
    <RefreshCw className="w-4 h-4 animate-spin text-blue-600" />
    <ElapsedTime startTime={task.started_at} />
  </div>
)}
```

---

### **Fix 7: Async File Upload**

**File:** `backend/app/api/routes/videos.py`

```python
# CURRENT (Synchronous)
with open(temp_path, 'wb') as f:
    content = await file.read()  # ← Reads entire file into memory
    f.write(content)

# NEW (Streaming)
async def stream_to_s3(file: UploadFile, s3_key: str):
    """Stream file directly to S3 without temp file"""
    import aioboto3
    
    session = aioboto3.Session()
    async with session.client('s3') as s3:
        # Multipart upload for large files
        mpu = await s3.create_multipart_upload(
            Bucket=settings.S3_BUCKET,
            Key=s3_key
        )
        
        parts = []
        part_number = 1
        chunk_size = 10 * 1024 * 1024  # 10MB chunks
        
        while True:
            chunk = await file.read(chunk_size)
            if not chunk:
                break
            
            response = await s3.upload_part(
                Bucket=settings.S3_BUCKET,
                Key=s3_key,
                PartNumber=part_number,
                UploadId=mpu['UploadId'],
                Body=chunk
            )
            
            parts.append({
                'PartNumber': part_number,
                'ETag': response['ETag']
            })
            part_number += 1
        
        # Complete upload
        await s3.complete_multipart_upload(
            Bucket=settings.S3_BUCKET,
            Key=s3_key,
            UploadId=mpu['UploadId'],
            MultipartUpload={'Parts': parts}
        )
```

---

## 📊 EXPECTED IMPROVEMENTS

### **Before:**
- Upload: 2-5 minutes (synchronous)
- Progress: Misleading (shows 100% too early)
- Timestamps: Missing for 3 tasks
- Object Detection: Delayed (waits for summary)
- Workers: 4 concurrent tasks
- Total Time: 8-10 minutes

### **After:**
- Upload: 30-60 seconds (streaming)
- Progress: Accurate (elapsed time)
- Timestamps: All tasks tracked
- Object Detection: Parallel (starts immediately)
- Workers: 16 concurrent tasks (fast) + 16 (slow)
- Total Time: 3-5 minutes

---

## 🎯 IMPLEMENTATION ORDER

### **Priority 1: Quick Wins** (1 hour)
1. ✅ Remove misleading 100% progress
2. ✅ Add embedding completion timestamp
3. ✅ Trigger object detection in parallel
4. ✅ Increase worker concurrency to 16

### **Priority 2: Frontend** (1 hour)
5. ✅ Replace progress bars with elapsed timers
6. ✅ Fix timestamp display

### **Priority 3: Optimization** (2 hours)
7. ✅ Add task queues (fast/slow)
8. ✅ Implement streaming upload

### **Priority 4: Advanced** (2 hours)
9. ⚠️ Add worker pools
10. ⚠️ Add task prioritization

---

## 📁 FILES TO MODIFY

### **Backend:**
1. `backend/app/tasks/video_tasks.py` - Fix progress, parallel tasks
2. `backend/app/tasks/embedding_tasks.py` - Add completion timestamp
3. `backend/app/api/routes/videos.py` - Streaming upload
4. `backend/app/core/celery_app.py` - Task routing
5. `docker-compose.yml` - Worker configuration

### **Frontend:**
6. `frontend/src/pages/JobsPage.tsx` - Elapsed time component
7. `frontend/src/pages/VideosPage.tsx` - Update status display

---

## ✅ SUCCESS CRITERIA

1. ✅ No misleading progress bars
2. ✅ All tasks show start/end timestamps
3. ✅ Object detection runs in parallel
4. ✅ Upload completes in < 1 minute
5. ✅ All tasks complete in < 5 minutes
6. ✅ 16+ concurrent tasks supported
7. ✅ Elapsed time shown for running tasks

---

**Total Estimated Time:** 6-8 hours  
**Priority:** High  
**Impact:** Significant performance improvement
