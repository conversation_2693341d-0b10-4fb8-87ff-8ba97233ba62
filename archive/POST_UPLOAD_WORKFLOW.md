# Post-Upload Workflow - Complete Documentation

## 📋 What Happens After Video Upload

### **Complete Workflow:**

```
┌─────────────────────────────────────────────────────────────┐
│                    VIDEO UPLOAD COMPLETE                    │
└─────────────────────────────────────────────────────────────┘
                            ↓
┌─────────────────────────────────────────────────────────────┐
│ Step 1: Upload to AWS S3                                    │
│ ✅ Video file streamed directly to S3                       │
│ ✅ No temporary storage on server                           │
│ ✅ Multipart upload for large files (10MB chunks)           │
│ ✅ S3 Key: videos/{video_id}/{filename}                     │
└─────────────────────────────────────────────────────────────┘
                            ↓
┌─────────────────────────────────────────────────────────────┐
│ Step 2: Metadata Extraction                                 │
│ ✅ Duration (seconds)                                        │
│ ✅ Resolution (width x height)                              │
│ ✅ FPS (frames per second)                                  │
│ ✅ File size (bytes)                                        │
│ ✅ Codec information                                        │
└─────────────────────────────────────────────────────────────┘
                            ↓
┌─────────────────────────────────────────────────────────────┐
│ Step 3: Parallel Task Execution (3 tasks simultaneously)   │
└─────────────────────────────────────────────────────────────┘
         ↓                    ↓                    ↓
    ┌────────┐          ┌────────┐          ┌────────┐
    │Task 1  │          │Task 2  │          │Task 3  │
    │Embed   │          │Summary │          │Detect  │
    └────────┘          └────────┘          └────────┘
```

---

## 🔄 Detailed Step-by-Step Process

### **Step 1: Upload to AWS S3** ✅

**What Happens:**
- Video file is streamed directly to S3
- No temporary file storage on server
- Multipart upload for files > 10MB
- Upload timestamps captured (start & end)

**Technical Details:**
```python
# Location: backend/app/services/upload_service.py
- Chunk size: 10MB
- Method: Multipart upload via aioboto3
- Region: us-east-1 (AWS Bedrock region)
- Bucket: poc-video-search-bucket
```

**S3 Structure:**
```
s3://poc-video-search-bucket/
  └── videos/
      └── {video_id}/
          └── {filename}.mp4
```

**Database Updates:**
```python
video.upload_started_at = datetime.utcnow()  # At start
video.upload_completed_at = datetime.utcnow()  # After S3 upload
video.status = VideoStatus.UPLOADING → VideoStatus.UPLOADED
```

---

### **Step 2: Metadata Extraction** ✅

**What Happens:**
- Video downloaded from S3 to temp location
- FFmpeg extracts metadata
- Database updated with video properties
- Temp file cleaned up

**Metadata Extracted:**
```python
{
    "duration": 120.5,      # seconds
    "width": 1920,          # pixels
    "height": 1080,         # pixels
    "fps": 30.0,            # frames per second
    "codec": "h264",        # video codec
    "bitrate": 5000000,     # bits per second
    "file_size": 75000000   # bytes
}
```

**Task Details:**
- **Queue:** `fast` (8 workers)
- **Task:** `process_video_metadata`
- **Location:** `backend/app/tasks/video_tasks.py`

**Database Updates:**
```python
video.duration = metadata['duration']
video.width = metadata['width']
video.height = metadata['height']
video.fps = metadata['fps']
video.status = VideoStatus.UPLOADED
```

---

### **Step 3: Parallel Processing (3 Tasks)** ✅

After metadata extraction, **3 tasks are triggered simultaneously:**

---

## 📊 Task 1: Embedding Generation

### **Purpose:**
Generate vector embeddings for semantic video search using AWS Bedrock models.

### **Models Used:**
Currently uses **ONE model** based on video configuration:
- **Nova Premier** (default) - `us.amazon.nova-premier-v1:0`
- **Marengo** (alternative) - `twelvelabs.marengo-embed-2-7-v1:0`

> **Note:** Currently only ONE model is used per video, not both. The model is selected during upload.

### **Process:**
```
1. Start AWS Bedrock async job
   ↓
2. Job processes video (2-5 minutes)
   ↓
3. Periodic check (every 30 seconds)
   ↓
4. Download embeddings from S3
   ↓
5. Ingest into Weaviate vector database
```

### **Technical Details:**
```python
# Queue: slow (16 workers)
# Task: start_embedding_generation
# Location: backend/app/tasks/embedding_tasks.py

# AWS Bedrock Configuration
Model: Nova Premier or Marengo (one selected)
Input: S3 video file
Output: S3 embeddings JSON
Processing Time: 2-5 minutes
Embedding Dimensions: 1024 (Nova) or 1536 (Marengo)
```

### **Output Structure:**
```json
{
  "embeddings": [
    {
      "timestamp": 0.0,
      "embedding": [0.123, 0.456, ...],  // 1024 or 1536 dimensions
      "frame_number": 0
    },
    {
      "timestamp": 1.0,
      "embedding": [0.789, 0.012, ...],
      "frame_number": 30
    }
    // ... more embeddings
  ],
  "total_embeddings": 300,
  "video_duration": 300.0
}
```

### **Database Updates:**
```python
video.embedding_started_at = datetime.utcnow()
video.embedding_completed_at = datetime.utcnow()
video.embedding_job_arn = "arn:aws:bedrock:..."
video.status = VideoStatus.INDEXED  # After completion
```

---

## 🔍 Task 2: Video Indexing in Weaviate

### **Purpose:**
Store embeddings in Weaviate vector database for semantic search.

### **Process:**
```
1. Receive embeddings from Bedrock
   ↓
2. Create Weaviate objects for each embedding
   ↓
3. Store with metadata (timestamp, video_id)
   ↓
4. Enable semantic search queries
```

### **Weaviate Schema:**
```python
Class: VideoEmbeddings
Properties:
  - video_id: string
  - timestamp: number
  - frame_number: number
  - embedding: vector (1024 or 1536 dimensions)
  - metadata: object
```

### **Storage:**
```
Weaviate Instance: http://weaviate:8080
Collection: VideoEmbeddings
Total Objects: ~300 per video (1 per second)
```

### **Enables:**
- Semantic video search
- Find similar moments across videos
- Natural language queries
- Time-based retrieval

---

## 📝 Task 3: Safety Summary Generation (MANDATORY)

### **Purpose:**
Generate AI-powered safety analysis report for fleet management.

### **Process:**
```
1. Download video from S3
   ↓
2. Send to AWS Bedrock (Nova Premier)
   ↓
3. AI analyzes video for safety incidents
   ↓
4. Generate structured safety report
   ↓
5. Store in database
```

### **What's Analyzed:**
- **Safety Violations:** Speeding, unsafe lane changes, tailgating
- **Near Misses:** Close calls, sudden braking
- **Unsafe Behaviors:** Distracted driving, aggressive driving
- **Environmental Hazards:** Poor weather, road conditions
- **Incident Severity:** Critical, High, Medium, Low

### **Output Format:**
```json
{
  "summary": "Video shows multiple safety incidents...",
  "incidents": [
    {
      "type": "unsafe_lane_change",
      "timestamp": 45.2,
      "severity": "high",
      "description": "Driver changed lanes without signaling..."
    }
  ],
  "risk_score": 7.5,
  "recommendations": [
    "Driver training on lane change procedures",
    "Review defensive driving techniques"
  ]
}
```

### **Technical Details:**
```python
# Queue: slow (16 workers)
# Task: generate_video_summary
# Location: backend/app/tasks/video_tasks.py

# AWS Bedrock Configuration
Model: Nova Premier (us.amazon.nova-premier-v1:0)
Max Tokens: 4096
Temperature: 0.3
Processing Time: 30-60 seconds
```

### **Database Updates:**
```python
video.summary_started_at = datetime.utcnow()
video.summary = json_summary
video.summary_completed_at = datetime.utcnow()
```

### **Status:**
✅ **MANDATORY** - Always generated for every video
❌ **NOT OPTIONAL** - Cannot be disabled

---

## 🎯 Task 4: Object Detection (Bonus)

### **Purpose:**
Detect and track objects in video using YOLOv8.

### **Objects Detected:**
- Vehicles: car, truck, bus, motorcycle
- People: person, pedestrian
- Traffic elements: traffic light, stop sign
- Other: bicycle, backpack, etc.

### **Process:**
```
1. Download video from S3
   ↓
2. Process frames with YOLOv8
   ↓
3. Detect objects in each frame
   ↓
4. Generate object summary
   ↓
5. Store results in database
```

### **Output Format:**
```json
{
  "object_summary": {
    "car": 759,
    "truck": 102,
    "traffic_light": 88,
    "person": 7,
    "bus": 8
  },
  "total_frames": 3600,
  "processed_frames": 120,
  "fps": 30.0,
  "started_at": "2025-11-01T04:28:38Z",
  "completed_at": "2025-11-01T04:31:52Z",
  "duration_seconds": 194
}
```

### **Technical Details:**
```python
# Queue: slow (16 workers)
# Task: detect_objects_in_video
# Location: backend/app/tasks/video_tasks.py

# YOLOv8 Configuration
Model: yolov8n.pt (nano - fast)
Sample Rate: 1 frame per second
Confidence Threshold: 0.5
Processing Time: 3-5 minutes for 5-minute video
```

---

## ⚡ Parallel Execution Timeline

### **Example: 5-minute video upload**

```
Time:  0s    10s   20s   30s   40s   50s   60s   ... 300s
       │     │     │     │     │     │     │         │
Upload ████████                                       │
       │                                              │
Metadata    ████                                      │
       │                                              │
Embed       ████████████████████████████████████████████
       │                                              │
Summary     ████████████                              │
       │                                              │
Detection   ████████████████████                      │
       │                                              │
Status: UPLOADING → UPLOADED → PROCESSING → INDEXED
```

### **Timeline Breakdown:**
- **0-10s:** Upload to S3
- **10-15s:** Metadata extraction
- **15s:** Trigger 3 parallel tasks
- **15-300s:** Embedding generation (longest)
- **15-75s:** Summary generation
- **15-210s:** Object detection
- **300s:** All complete, video INDEXED

---

## 📊 Current Implementation Status

| Step | Status | Details |
|------|--------|---------|
| **Upload to S3** | ✅ Complete | Streaming multipart upload |
| **Metadata Extraction** | ✅ Complete | Duration, resolution, FPS |
| **Embedding Generation** | ⚠️ Single Model | Uses ONE model (Nova or Marengo), not both |
| **Weaviate Indexing** | ✅ Complete | Semantic search enabled |
| **Safety Summary** | ✅ Mandatory | Always generated |
| **Object Detection** | ✅ Complete | YOLOv8 detection |

---

## 🔧 Configuration

### **Model Selection:**
Currently, videos use **ONE embedding model**:
- Default: Nova Premier
- Alternative: Marengo
- Selected during upload (hardcoded to `nova-premier` in current implementation)

### **To Use Both Models:**
Would require modification to:
1. Generate embeddings with both models
2. Store both sets in Weaviate
3. Query both during search
4. Merge/rank results

**Current:** Single model per video
**Requested:** Both models (requires implementation)

---

## 🎯 Summary

### **What Happens After Upload:**

1. ✅ **Video uploaded to AWS S3** - Streaming multipart upload
2. ✅ **Metadata extracted** - Duration, resolution, FPS
3. ⚠️ **Embeddings generated** - ONE model (Nova Premier OR Marengo), not both
4. ✅ **Video indexed in Weaviate** - Semantic search enabled
5. ✅ **Safety summary generated** - MANDATORY (not optional)
6. ✅ **Objects detected** - YOLOv8 analysis

### **Key Points:**
- All tasks run in **parallel** (except metadata which is sequential)
- **28 Celery workers** handle concurrent processing
- **Summary is mandatory** - always generated
- **Single embedding model** - not both models simultaneously
- **Complete in 2-5 minutes** for typical video

---

## 🚀 Next Steps

### **To Enable Both Models:**
1. Modify embedding task to generate with both models
2. Update Weaviate schema to support multiple models
3. Implement search across both model embeddings
4. Update UI to show which models were used

**Current Status:** Single model implementation
**Effort Required:** Medium (2-3 hours development)
