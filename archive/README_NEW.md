# Samsara Video Search POC v2.0

Modern video search application with FastAPI backend and React TypeScript frontend.

## Architecture

### Technology Stack
- **Backend**: FastAPI (Python 3.11)
- **Frontend**: React 18 + TypeScript
- **Database**: PostgreSQL 15
- **Vector DB**: Weaviate
- **Cache/Queue**: Redis
- **Task Queue**: Celery
- **Video Processing**: YOLOv8, MediaPipe
- **ML Models**: AWS Bedrock (Nova Premier, TwelveLabs Marengo)

### Services
1. **FastAPI Backend** (Port 8000) - REST API
2. **React Frontend** (Port 3000) - Web UI
3. **PostgreSQL** (Port 5432) - Relational data
4. **Weaviate** (Port 8080) - Vector embeddings
5. **Redis** (Port 6379) - Cache & message broker
6. **Celery Worker** - Async task processing
7. **Celery Beat** - Periodic tasks

## Features

### Core Capabilities
- ✅ Video upload (file & YouTube)
- ✅ Async embedding generation (AWS Bedrock)
- ✅ Semantic video search
- ✅ Multi-model support (Nova Premier, Marengo)
- ✅ Video processing with YOLOv8
- ✅ PII blurring (faces, license plates)
- ✅ Object tracking with trajectories
- ✅ AI-powered safety summaries
- ✅ Search result ratings
- ✅ Model performance analytics

### Key Improvements over v1.0
- Async processing with Celery
- Proper state management
- WebSocket for real-time updates
- Better error handling
- Scalable architecture
- Clean separation of concerns

## Quick Start

### Prerequisites
- Docker & Docker Compose
- AWS credentials (see `.env`)
- 8GB+ RAM recommended

### Setup

1. **Clone and navigate**:
   ```bash
   cd /Users/<USER>/repos/samsara-poc-main
   ```

2. **Environment variables** (already configured in `.env`):
   ```
   AWS_ACCESS_KEY_ID=********************
   AWS_SECRET_ACCESS_KEY=jiTJCbKTcCROcAaIsDBqWOXuPxIN3qP4RQme2x1/
   AWS_ACCOUNT_ID=************
   S3_BUCKET=poc-video-search-bucket
   BEDROCK_REGION=us-east-1
   ```

3. **Start all services**:
   ```bash
   docker-compose up -d
   ```

4. **Check status**:
   ```bash
   docker-compose ps
   ```

5. **View logs**:
   ```bash
   docker-compose logs -f backend
   docker-compose logs -f celery-worker
   ```

### Access Points
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8000
- **API Docs**: http://localhost:8000/docs
- **Weaviate**: http://localhost:8080
- **PostgreSQL**: localhost:5432

### Default Credentials
- **Password**: `minfy2025`

## Project Structure

```
samsara-poc-main/
├── backend/
│   ├── app/
│   │   ├── api/
│   │   │   └── routes/
│   │   │       ├── auth.py
│   │   │       ├── videos.py (TODO)
│   │   │       ├── search.py (TODO)
│   │   │       └── analytics.py (TODO)
│   │   ├── core/
│   │   │   ├── config.py
│   │   │   ├── database.py
│   │   │   ├── security.py
│   │   │   └── celery_app.py
│   │   ├── models/
│   │   │   └── video.py
│   │   ├── schemas/
│   │   │   ├── auth.py
│   │   │   └── video.py
│   │   ├── services/
│   │   │   ├── aws_service.py
│   │   │   ├── vector_service.py
│   │   │   └── video_service.py
│   │   ├── tasks/
│   │   │   ├── video_tasks.py
│   │   │   └── embedding_tasks.py
│   │   └── main.py
│   ├── Dockerfile
│   └── requirements.txt
├── frontend/ (TODO)
│   ├── src/
│   │   ├── components/
│   │   ├── pages/
│   │   ├── services/
│   │   └── App.tsx
│   ├── Dockerfile
│   └── package.json
├── docker-compose.yml
├── .env
├── app.py (OLD - keep for reference)
└── README_NEW.md
```

## Development Workflow

### Backend Development
```bash
# Enter backend container
docker-compose exec backend bash

# Run migrations
alembic upgrade head

# Create new migration
alembic revision --autogenerate -m "description"

# Run tests
pytest
```

### Frontend Development
```bash
# Enter frontend container
docker-compose exec frontend bash

# Install dependencies
npm install

# Run tests
npm test
```

### Celery Monitoring
```bash
# View worker status
docker-compose exec celery-worker celery -A app.core.celery_app inspect active

# View scheduled tasks
docker-compose exec celery-beat celery -A app.core.celery_app inspect scheduled
```

## API Endpoints

### Authentication
- `POST /api/auth/login` - Login with password
- `POST /api/auth/verify` - Verify token

### Videos (TODO)
- `POST /api/videos/upload` - Upload video
- `GET /api/videos` - List videos
- `GET /api/videos/{video_id}` - Get video details
- `DELETE /api/videos/{video_id}` - Delete video
- `POST /api/videos/{video_id}/index` - Start indexing

### Search (TODO)
- `POST /api/search` - Semantic search
- `POST /api/search/rate` - Rate search result

### Analytics (TODO)
- `GET /api/analytics/ratings` - Get rating statistics
- `GET /api/analytics/models` - Compare model performance

### Jobs (TODO)
- `GET /api/jobs/{job_id}` - Get job status
- `GET /api/jobs/video/{video_id}` - Get video jobs

## Async Processing Flow

1. **Video Upload**:
   - User uploads video → FastAPI endpoint
   - File saved to temp → Celery task triggered
   - Task uploads to S3 → Extracts metadata → Updates DB

2. **Embedding Generation**:
   - User triggers indexing → Start AWS Bedrock async job
   - Celery Beat checks S3 every 30s for results
   - When ready → Ingest to Weaviate → Update status

3. **Video Search**:
   - User searches → Generate query embedding
   - Search Weaviate → Get top results
   - Extract clips on-demand → Cache in S3
   - Optional: Process with YOLOv8 (async)

4. **Summary Generation**:
   - Triggered after upload → Celery task
   - Call Nova Premier → Save to S3 → Update DB

## Configuration

### Environment Variables
See `.env` file for all configuration options.

### Celery Configuration
- **Concurrency**: 2 workers (adjust based on CPU)
- **Task time limit**: 1 hour
- **Beat schedule**: Check embeddings every 30s

### Weaviate Configuration
- **Vectorizer**: None (we provide embeddings)
- **Distance metric**: Cosine similarity
- **HNSW index**: Default settings

## Monitoring & Debugging

### Logs
```bash
# All services
docker-compose logs -f

# Specific service
docker-compose logs -f backend
docker-compose logs -f celery-worker
```

### Database
```bash
# Connect to PostgreSQL
docker-compose exec postgres psql -U samsara -d samsara_db

# Common queries
SELECT * FROM videos;
SELECT * FROM processing_jobs WHERE status = 'running';
```

### Weaviate
```bash
# Check collection
curl http://localhost:8080/v1/schema

# Count objects
curl http://localhost:8080/v1/objects?class=VideoEmbeddings
```

## Troubleshooting

### Common Issues

1. **Container won't start**:
   ```bash
   docker-compose down -v
   docker-compose up --build
   ```

2. **Database connection error**:
   - Check PostgreSQL is healthy: `docker-compose ps`
   - Verify DATABASE_URL in backend

3. **Celery tasks not running**:
   - Check Redis: `docker-compose logs redis`
   - Check worker: `docker-compose logs celery-worker`

4. **Weaviate connection error**:
   - Wait for health check: `docker-compose ps weaviate`
   - Check URL: http://localhost:8080/v1/.well-known/ready

## Next Steps (TODO)

### Backend
- [ ] Complete video routes (upload, list, delete)
- [ ] Complete search routes
- [ ] Complete analytics routes
- [ ] Add WebSocket support for real-time updates
- [ ] Add rate limiting
- [ ] Add comprehensive error handling
- [ ] Write unit tests

### Frontend
- [ ] Create React app structure
- [ ] Implement authentication
- [ ] Build upload page with progress
- [ ] Build search page with results
- [ ] Build video list page
- [ ] Build analytics dashboard
- [ ] Add WebSocket client
- [ ] Implement state management (React Query + Zustand)

### Infrastructure
- [ ] Add Nginx reverse proxy
- [ ] Set up CloudFront for video delivery
- [ ] Add monitoring (Prometheus + Grafana)
- [ ] Set up CI/CD pipeline
- [ ] Add backup strategy

## Migration from v1.0

The old Streamlit app (`app.py`) is kept for reference. Key differences:

| Feature | v1.0 (Streamlit) | v2.0 (FastAPI + React) |
|---------|------------------|------------------------|
| Frontend | Streamlit | React + TypeScript |
| Backend | Monolithic | Microservices |
| Async | Threading | Celery |
| Vector DB | ChromaDB | Weaviate |
| State | Session state | PostgreSQL + Redis |
| Real-time | Auto-refresh | WebSocket |
| Scalability | Single instance | Horizontal scaling |

## Performance Targets

- Video upload: < 5s for 100MB
- Embedding generation: 2-5 minutes (AWS Bedrock)
- Search latency: < 500ms
- Clip extraction: < 3s
- Video processing: Real-time (30fps+)

## Security Notes

- Simple password auth (POC only)
- JWT tokens for API access
- AWS credentials in environment variables
- No user registration (single admin)
- CORS configured for localhost

## License

Internal POC - Minfy Technologies

## Support

For issues or questions, contact the development team.
