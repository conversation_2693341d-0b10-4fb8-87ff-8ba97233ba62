# Workflow Redesign - Implementation Status

## ✅ Completed Tasks

### 1. Database Migration ✅
- **File:** `backend/app/models/video.py`
- **Changes:**
  - Removed single `embedding_model` field
  - Added `nova_embedding_started_at`, `nova_embedding_completed_at`, `nova_embedding_arn`, `nova_indexed_at`
  - Added `marengo_embedding_started_at`, `marengo_embedding_completed_at`, `marengo_embedding_arn`, `marengo_indexed_at`
  - Migration created and applied: `777fbafffa25_add_dual_embedding_fields.py`

### 2. Celery Service Consolidation ✅
- **File:** `docker-compose.yml`
- **Changes:**
  - Removed `celery-worker-fast` (8 workers)
  - Removed `celery-worker-slow` (16 workers)
  - Consolidated into single `celery-worker` with 20 workers
  - Removed queue routing from `backend/app/core/celery_app.py`

### 3. Workflow Coordinator ✅
- **File:** `backend/app/tasks/workflow_tasks.py` (NEW)
- **Functions:**
  - `trigger_dual_embeddings()` - Starts both Nova and Marengo embeddings in parallel
  - `on_embeddings_complete()` - Called when both embeddings finish, triggers summary
  - `on_summary_complete()` - Called when summary finishes, marks video as INDEXED

### 4. Summary Generation with Embeddings ✅
- **File:** `backend/app/tasks/summary_tasks.py` (NEW)
- **Functions:**
  - `generate_summary_from_embeddings()` - Main task
  - `fetch_marengo_embeddings()` - Retrieves embeddings from Weaviate
  - `cluster_scenes()` - Uses DBSCAN to cluster embeddings into scenes
  - `generate_scene_summaries()` - Summarizes each scene
  - `generate_safety_report()` - Creates overall safety report

---

## 🚧 Remaining Tasks

### 5. Update Embedding Tasks ⏳
**Need to modify:** `backend/app/tasks/embedding_tasks.py`

**Changes needed:**
```python
@celery_app.task(bind=True, base=DatabaseTask)
def start_embedding_generation(self, video_id: str, s3_key: str, model: str):
    # Update to set model-specific timestamps
    if model == "nova-premier":
        video.nova_embedding_started_at = datetime.utcnow()
        # ... after completion
        video.nova_embedding_completed_at = datetime.utcnow()
        video.nova_indexed_at = datetime.utcnow()
    elif model == "marengo":
        video.marengo_embedding_started_at = datetime.utcnow()
        # ... after completion
        video.marengo_embedding_completed_at = datetime.utcnow()
        video.marengo_indexed_at = datetime.utcnow()
```

### 6. Update Upload Endpoints ⏳
**Need to modify:**
- `backend/app/api/routes/videos.py` - `upload_video()`
- `backend/app/tasks/youtube_tasks.py` - `download_youtube_video()`

**Changes needed:**
```python
# Replace current embedding trigger with:
from app.tasks.workflow_tasks import trigger_dual_embeddings

# After upload completes
trigger_dual_embeddings.delay(video_id, s3_key)
```

### 7. Add Vector Service Method ⏳
**Need to add:** `backend/app/services/vector_service.py`

**New method:**
```python
def get_video_embeddings(self, video_id: str, model: str = "marengo"):
    """Fetch all embeddings for a video from Weaviate"""
    # Query Weaviate for embeddings
    # Filter by video_id and model
    # Return list of embeddings with timestamps
```

### 8. Move Object Detection to Search ⏳
**Need to modify:** `backend/app/api/routes/search.py`

**Changes needed:**
```python
@router.post("/search")
async def search_videos(request: SearchRequest):
    # 1. Perform semantic search
    results = vector_service.search(...)
    
    # 2. For each result, extract segment
    # 3. Run YOLO object detection
    # 4. Apply PII blurring (faces, plates)
    # 5. Return processed segments
```

**Remove from upload:**
- Remove `detect_objects_in_video` from YouTube task
- Remove `detect_objects_in_video` from video upload task

### 9. Update Frontend UI ⏳
**Need to modify:** `frontend/src/pages/JobsPage.tsx`

**Changes needed:**
- Show both Nova and Marengo embedding progress
- Update timestamp display for dual embeddings
- Remove object detection from upload tasks
- Add note that object detection happens at search-time

### 10. Restart Services ⏳
**Commands:**
```bash
docker-compose down
docker-compose up -d
```

---

## 📋 Implementation Checklist

- [x] Database migration for dual embeddings
- [x] Consolidate Celery services
- [x] Create workflow coordinator
- [x] Create summary generation with clustering
- [ ] Update embedding tasks for dual model tracking
- [ ] Update upload endpoints to use new workflow
- [ ] Add vector service method for fetching embeddings
- [ ] Move object detection to search endpoint
- [ ] Update frontend UI
- [ ] Restart services and test

---

## 🎯 New Workflow (Implemented)

```
Upload Complete
       ↓
trigger_dual_embeddings()
       ↓
   ┌────────────────────┐
   │  Parallel Start    │
   ├──────────┬─────────┤
   │  Nova    │ Marengo │
   │  Embed   │ Embed   │
   └──────────┴─────────┘
       ↓
   Wait for Both
       ↓
on_embeddings_complete()
       ↓
generate_summary_from_embeddings()
  ├─ Fetch Marengo embeddings
  ├─ Cluster scenes (DBSCAN)
  ├─ Generate scene summaries
  └─ Create safety report
       ↓
on_summary_complete()
       ↓
Video Status = INDEXED

(Object Detection moved to search-time)
```

---

## 🔧 Quick Start Guide

### To Complete Implementation:

1. **Update embedding tasks:**
   ```bash
   # Edit: backend/app/tasks/embedding_tasks.py
   # Add model-specific timestamp tracking
   ```

2. **Update upload triggers:**
   ```bash
   # Edit: backend/app/api/routes/videos.py
   # Edit: backend/app/tasks/youtube_tasks.py
   # Replace embedding calls with trigger_dual_embeddings
   ```

3. **Add vector service method:**
   ```bash
   # Edit: backend/app/services/vector_service.py
   # Add get_video_embeddings() method
   ```

4. **Move object detection:**
   ```bash
   # Edit: backend/app/api/routes/search.py
   # Add YOLO + PII processing to search endpoint
   ```

5. **Restart services:**
   ```bash
   docker-compose down
   docker-compose up -d
   ```

---

## 📊 Progress: 60% Complete

**Completed:** 4/7 major tasks
**Remaining:** 3/7 major tasks
**Estimated time:** 4-6 hours

---

## 🚀 Next Steps

1. Update embedding tasks (1 hour)
2. Update upload endpoints (1 hour)
3. Add vector service method (30 min)
4. Move object detection to search (2 hours)
5. Update frontend UI (1 hour)
6. Test end-to-end (1 hour)

**Total remaining:** ~6 hours
