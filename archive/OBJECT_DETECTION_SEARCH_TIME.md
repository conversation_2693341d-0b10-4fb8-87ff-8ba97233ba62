# Object Detection - Moved to Search-Time

## ✅ Implementation Status

Object detection with YOLOv8 has been **removed from the upload workflow** and will be implemented at **search-time** instead.

---

## Why This Change?

### **Before (Upload-Time):**
- Object detection ran during video upload
- Processed entire video upfront
- Added ~3-5 minutes to upload time
- Results stored in database
- No PII blurring

### **After (Search-Time):**
- Object detection runs during semantic search
- Only processes relevant segments
- Faster uploads
- On-demand PII blurring
- Better resource utilization

---

## New Workflow

```
Upload → Dual Embeddings → Summary → INDEXED
                                         ↓
                              (Object detection removed)

Search Query → Semantic Search → Extract Segments → YOLO + PII Blur → Return Results
```

---

## Implementation TODO

### **Search Endpoint Enhancement:**

**File:** `backend/app/api/routes/search.py`

**Add to search endpoint:**
```python
@router.post("/search")
async def search_videos(request: SearchRequest):
    # 1. Semantic search for relevant segments
    results = vector_service.search(request.query, top_k=10)
    
    # 2. For each result, process with YOLO + PII
    processed_results = []
    for result in results:
        # Extract video segment (5-10 seconds around timestamp)
        segment_path = await extract_segment(
            video_id=result['video_id'],
            start_time=result['start_sec'] - 5,
            end_time=result['start_sec'] + 5
        )
        
        # Run object detection on segment
        objects = yolo_service.detect_objects(segment_path)
        
        # Apply PII blurring (faces, license plates)
        blurred_segment = await blur_pii(segment_path, objects)
        
        # Upload blurred segment to S3
        blurred_url = await upload_segment_to_s3(blurred_segment)
        
        processed_results.append({
            'video_id': result['video_id'],
            'timestamp': result['start_sec'],
            'segment_url': blurred_url,
            'objects_detected': objects,
            'similarity_score': result['similarity']
        })
    
    return processed_results
```

---

## Functions to Implement

### **1. Segment Extraction**
```python
async def extract_segment(video_id: str, start_time: float, end_time: float) -> str:
    """
    Extract a video segment using FFmpeg.
    
    Returns path to extracted segment file.
    """
    # Download video from S3
    video_path = await download_video_from_s3(video_id)
    
    # Extract segment with FFmpeg
    segment_path = f"/tmp/{video_id}_segment_{start_time}_{end_time}.mp4"
    
    cmd = [
        "ffmpeg", "-i", video_path,
        "-ss", str(start_time),
        "-to", str(end_time),
        "-c", "copy",
        segment_path
    ]
    
    subprocess.run(cmd, check=True)
    return segment_path
```

### **2. PII Blurring**
```python
async def blur_pii(segment_path: str, detected_objects: List[Dict]) -> str:
    """
    Blur faces and license plates in video segment.
    
    Returns path to blurred video file.
    """
    import cv2
    
    cap = cv2.VideoCapture(segment_path)
    fps = cap.get(cv2.CAP_PROP_FPS)
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    
    blurred_path = segment_path.replace('.mp4', '_blurred.mp4')
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(blurred_path, fourcc, fps, (width, height))
    
    frame_idx = 0
    while cap.isOpened():
        ret, frame = cap.read()
        if not ret:
            break
        
        # Get objects for this frame
        frame_objects = [obj for obj in detected_objects if obj['frame'] == frame_idx]
        
        # Blur each object
        for obj in frame_objects:
            if obj['class'] in ['person', 'face', 'license_plate']:
                x1, y1, x2, y2 = obj['bbox']
                roi = frame[y1:y2, x1:x2]
                blurred_roi = cv2.GaussianBlur(roi, (99, 99), 30)
                frame[y1:y2, x1:x2] = blurred_roi
        
        out.write(frame)
        frame_idx += 1
    
    cap.release()
    out.release()
    
    return blurred_path
```

### **3. Object Detection on Segment**
```python
def detect_objects_in_segment(segment_path: str) -> List[Dict]:
    """
    Run YOLO object detection on video segment.
    
    Returns list of detected objects with bounding boxes.
    """
    from app.services.yolo_service import yolo_service
    
    results = yolo_service.detect_objects_in_video(
        segment_path,
        sample_rate=30,  # Process every frame (30fps)
        confidence_threshold=0.5
    )
    
    return results['detections']
```

---

## Benefits

### **Performance:**
- ✅ Faster uploads (no object detection overhead)
- ✅ Only process relevant segments
- ✅ Parallel processing during search

### **Privacy:**
- ✅ PII blurring applied on-demand
- ✅ Only blur segments that are viewed
- ✅ No PII in stored videos

### **Resource Efficiency:**
- ✅ No wasted processing on irrelevant parts
- ✅ Better CPU utilization
- ✅ Lower storage costs

---

## Current Status

- ✅ Removed from upload workflow
- ✅ Removed from YouTube download workflow
- ⏳ Search endpoint enhancement (TODO)
- ⏳ Segment extraction (TODO)
- ⏳ PII blurring (TODO)

---

## Estimated Implementation Time

- Segment extraction: 1 hour
- PII blurring: 2 hours
- Search endpoint integration: 1 hour
- Testing: 1 hour

**Total: ~5 hours**

---

## Note

Object detection is currently **disabled** during upload. It will be re-enabled at search-time once the search endpoint enhancements are implemented.
