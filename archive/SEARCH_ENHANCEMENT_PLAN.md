# Search Enhancement Plan - Match Old Streamlit Functionality

## 📋 Current vs Required Functionality

### **Old Streamlit App (app.py) - Search Flow**

```
1. User enters search query
2. Generate text embedding (Nova or Marengo)
3. Search ChromaDB for top-K segments
4. For each result:
   a. Extract original clip from S3 video (FFmpeg)
   b. <PERSON><PERSON> original clip in S3
   c. Display original clip
   d. Process clip with YOLOv8/Rekognition:
      - Object detection & labeling
      - PII blurring (faces + license plates)
      - Object tracking (YOLOv8 only)
   e. <PERSON><PERSON> processed clip in S3
   f. Display processed clip
   g. Show AI summary if available
   h. Rating system
```

### **New FastAPI System - Current Implementation**

```
1. User enters search query
2. Generate text embedding (Marengo)
3. Search Weaviate for top-K segments
4. Return segment metadata with URLs
5. Frontend displays video segments
   ❌ NO clip extraction
   ❌ NO processing with YOLOv8
   ❌ NO PII blurring
   ❌ NO object labeling
```

---

## 🎯 Required Features (MUST HAVE)

### **1. Video Summarization** ✅ IMPLEMENTED
- **Status:** Already working
- **Location:** `backend/app/tasks/video_tasks.py` - `generate_video_summary`
- **Model:** Nova Premier
- **Output:** Stored in `summaries/{video_id}/safety_report.json`
- **Frontend:** Can be displayed in search results

### **2. Intelligent Search with Embeddings** ✅ IMPLEMENTED
- **Status:** Already working
- **Location:** `backend/app/api/routes/search.py`
- **Model:** Marengo for embeddings
- **Vector DB:** Weaviate
- **Deduplication:** Implemented in `vector_service.py`

### **3. PII Blurring** ⚠️ PARTIALLY IMPLEMENTED
- **Status:** Backend exists, not integrated in search flow
- **Location:** `backend/app/services/pii_service.py` (needs verification)
- **Methods:** 
  - Face blurring (MediaPipe)
  - License plate blurring (YOLOv8)
- **Required:** Integrate into search clip processing

### **4. Object Labeling** ⚠️ PARTIALLY IMPLEMENTED
- **Status:** Backend exists, not integrated in search flow
- **Location:** `backend/app/services/yolo_service.py` (needs verification)
- **Methods:**
  - YOLOv8 object detection
  - Bounding boxes
  - Object tracking
- **Required:** Integrate into search clip processing

---

## 🔧 Implementation Plan

### **Phase 1: Verify Existing Services** ✅

1. Check if `pii_service.py` exists
2. Check if `yolo_service.py` exists
3. Check if `video_service.py` has clip extraction
4. Verify YOLOv8 processing function

### **Phase 2: Enhance Search API** 🔄

**File:** `backend/app/api/routes/search.py`

**Changes Needed:**

1. **Add clip extraction endpoint**
   ```python
   @router.post("/extract-clip")
   async def extract_and_process_clip(
       video_id: str,
       start_sec: float,
       duration: float = 10.0,
       enable_labeling: bool = True,
       enable_pii_blur: bool = True,
       blur_faces: bool = True,
       blur_plates: bool = True,
       enable_tracking: bool = True,
       confidence_threshold: float = 0.5,
       blur_intensity: int = 35
   ):
       # 1. Extract clip from original video
       # 2. Process with YOLOv8
       # 3. Cache both original and processed
       # 4. Return URLs
   ```

2. **Update search response to include processing options**
   ```python
   class SearchResult:
       video_id: str
       segment_id: int
       timestamp: str
       start_sec: float
       similarity_score: float
       original_clip_url: Optional[str]  # NEW
       processed_clip_url: Optional[str]  # NEW
       processing_available: bool  # NEW
       summary: Optional[str]  # NEW
   ```

### **Phase 3: Update Frontend** 🔄

**File:** `frontend/src/pages/SearchPage.tsx`

**Changes Needed:**

1. **Add processing options UI**
   - Checkbox: Enable object labeling
   - Checkbox: Enable PII blurring
   - Checkbox: Blur faces
   - Checkbox: Blur license plates
   - Checkbox: Enable tracking
   - Slider: Confidence threshold
   - Slider: Blur intensity

2. **Display both original and processed clips**
   ```tsx
   {result.original_clip_url && (
     <div>
       <h4>Original Clip</h4>
       <video src={result.original_clip_url} controls />
     </div>
   )}
   
   {result.processed_clip_url && (
     <div>
       <h4>Processed Clip (with labels & PII blur)</h4>
       <video src={result.processed_clip_url} controls />
       <span>🏷️ Object Labels | 🔒 PII Blurred</span>
     </div>
   )}
   ```

3. **Add "Process Clip" button**
   - Triggers processing if not already cached
   - Shows progress bar
   - Updates UI when complete

4. **Display AI summary**
   ```tsx
   {result.summary && (
     <Accordion>
       <AccordionSummary>📋 AI Safety Summary</AccordionSummary>
       <AccordionDetails>{result.summary}</AccordionDetails>
     </Accordion>
   )}
   ```

---

## 📊 Detailed Implementation

### **Backend: Clip Extraction & Processing Service**

**File:** `backend/app/services/clip_service.py` (NEW)

```python
import os
import subprocess
import uuid
from typing import Optional, Dict, Any
from loguru import logger

class ClipService:
    """Service for extracting and processing video clips"""
    
    async def extract_clip(
        self,
        video_s3_key: str,
        start_sec: float,
        duration: float = 10.0
    ) -> str:
        """Extract clip from video using FFmpeg"""
        temp_input = f"/tmp/{uuid.uuid4()}_input.mp4"
        temp_output = f"/tmp/{uuid.uuid4()}_clip.mp4"
        
        try:
            # Download from S3
            aws_service.download_from_s3(video_s3_key, temp_input)
            
            # FFmpeg command
            cmd = [
                'ffmpeg', '-y', '-i', temp_input,
                '-ss', str(start_sec),
                '-t', str(duration),
                '-c:v', 'libx264',
                '-c:a', 'aac',
                '-preset', 'fast',
                '-crf', '23',
                '-avoid_negative_ts', 'make_zero',
                temp_output
            ]
            
            subprocess.run(cmd, check=True, capture_output=True)
            
            return temp_output
            
        finally:
            if os.path.exists(temp_input):
                os.remove(temp_input)
    
    async def process_clip_with_yolo(
        self,
        input_path: str,
        output_path: str,
        enable_labeling: bool = True,
        enable_pii_blur: bool = True,
        blur_faces: bool = True,
        blur_plates: bool = True,
        enable_tracking: bool = True,
        confidence_threshold: float = 0.5,
        blur_intensity: int = 35
    ) -> Dict[str, Any]:
        """Process clip with YOLOv8 and PII blurring"""
        from app.services.yolo_service import yolo_service
        from app.services.pii_service import pii_service
        
        # Process with YOLOv8
        result = yolo_service.process_video_with_yolo(
            input_path=input_path,
            output_path=output_path,
            enable_labeling=enable_labeling,
            enable_pii_blur=enable_pii_blur,
            blur_faces=blur_faces,
            blur_plates=blur_plates,
            enable_tracking=enable_tracking,
            confidence_threshold=confidence_threshold,
            blur_kernel=blur_intensity
        )
        
        return result

clip_service = ClipService()
```

### **Backend: Enhanced Search Endpoint**

**File:** `backend/app/api/routes/search.py`

```python
@router.post("/extract-and-process")
async def extract_and_process_clip(
    video_id: str,
    segment_id: int,
    start_sec: float,
    duration: float = 10.0,
    enable_labeling: bool = True,
    enable_pii_blur: bool = True,
    blur_faces: bool = True,
    blur_plates: bool = True,
    enable_tracking: bool = True,
    confidence_threshold: float = 0.5,
    blur_intensity: int = 35,
    db: Session = Depends(get_db_session),
    current_user: dict = Depends(get_current_user)
):
    """
    Extract clip and process with YOLOv8 + PII blurring
    
    This endpoint:
    1. Extracts clip from original video
    2. Caches original clip in S3
    3. Processes with YOLOv8 (object detection + tracking)
    4. Applies PII blurring (faces + license plates)
    5. Caches processed clip in S3
    6. Returns URLs for both clips
    """
    try:
        # Get video
        video = db.query(Video).filter(Video.video_id == video_id).first()
        if not video:
            raise HTTPException(status_code=404, detail="Video not found")
        
        # Generate cache keys
        cache_prefix = f"search-clips/{video_id}/{segment_id}"
        original_clip_key = f"{cache_prefix}/original.mp4"
        processed_clip_key = f"{cache_prefix}/processed.mp4"
        
        # Check if already cached
        if aws_service.s3_object_exists(original_clip_key) and \
           aws_service.s3_object_exists(processed_clip_key):
            logger.info(f"Using cached clips for {video_id}/{segment_id}")
            return {
                "video_id": video_id,
                "segment_id": segment_id,
                "original_clip_url": aws_service.generate_presigned_url(original_clip_key),
                "processed_clip_url": aws_service.generate_presigned_url(processed_clip_key),
                "cached": True
            }
        
        # Extract clip
        temp_clip = await clip_service.extract_clip(
            video_s3_key=video.s3_key,
            start_sec=start_sec,
            duration=duration
        )
        
        # Upload original to S3
        aws_service.upload_file_to_s3(temp_clip, original_clip_key)
        
        # Process clip
        temp_processed = f"/tmp/{uuid.uuid4()}_processed.mp4"
        result = await clip_service.process_clip_with_yolo(
            input_path=temp_clip,
            output_path=temp_processed,
            enable_labeling=enable_labeling,
            enable_pii_blur=enable_pii_blur,
            blur_faces=blur_faces,
            blur_plates=blur_plates,
            enable_tracking=enable_tracking,
            confidence_threshold=confidence_threshold,
            blur_intensity=blur_intensity
        )
        
        # Upload processed to S3
        aws_service.upload_file_to_s3(temp_processed, processed_clip_key)
        
        # Cleanup
        for f in [temp_clip, temp_processed]:
            if os.path.exists(f):
                os.remove(f)
        
        logger.info(f"Clips generated for {video_id}/{segment_id}")
        
        return {
            "video_id": video_id,
            "segment_id": segment_id,
            "original_clip_url": aws_service.generate_presigned_url(original_clip_key),
            "processed_clip_url": aws_service.generate_presigned_url(processed_clip_key),
            "cached": False,
            "processing_stats": result
        }
        
    except Exception as e:
        logger.error(f"Error processing clip: {e}")
        raise HTTPException(status_code=500, detail=str(e))
```

### **Frontend: Enhanced Search UI**

**File:** `frontend/src/pages/SearchPage.tsx`

```typescript
// Add processing options state
const [processingOptions, setProcessingOptions] = useState({
  enableLabeling: true,
  enablePiiBlur: true,
  blurFaces: true,
  blurPlates: true,
  enableTracking: true,
  confidenceThreshold: 0.5,
  blurIntensity: 35
});

// Add processing state
const [processingClips, setProcessingClips] = useState<Set<string>>(new Set());

// Function to process clip
const processClip = async (result: SearchResult) => {
  const clipKey = `${result.video_id}_${result.segment_id}`;
  setProcessingClips(prev => new Set(prev).add(clipKey));
  
  try {
    const response = await searchService.extractAndProcessClip({
      video_id: result.video_id,
      segment_id: result.segment_id,
      start_sec: result.start_sec,
      duration: 10.0,
      ...processingOptions
    });
    
    // Update result with clip URLs
    setResults(prevResults =>
      prevResults.map(r =>
        r.video_id === result.video_id && r.segment_id === result.segment_id
          ? {
              ...r,
              original_clip_url: response.original_clip_url,
              processed_clip_url: response.processed_clip_url
            }
          : r
      )
    );
  } catch (error) {
    console.error('Processing error:', error);
  } finally {
    setProcessingClips(prev => {
      const next = new Set(prev);
      next.delete(clipKey);
      return next;
    });
  }
};

// UI for processing options
<Accordion>
  <AccordionSummary>🎬 Video Processing Options</AccordionSummary>
  <AccordionDetails>
    <FormGroup>
      <FormControlLabel
        control={<Checkbox checked={processingOptions.enableLabeling} />}
        label="🏷️ Object Labeling"
      />
      <FormControlLabel
        control={<Checkbox checked={processingOptions.enablePiiBlur} />}
        label="🔒 PII Protection"
      />
      <FormControlLabel
        control={<Checkbox checked={processingOptions.blurFaces} />}
        label="👤 Blur Faces"
      />
      <FormControlLabel
        control={<Checkbox checked={processingOptions.blurPlates} />}
        label="🚗 Blur License Plates"
      />
      <FormControlLabel
        control={<Checkbox checked={processingOptions.enableTracking} />}
        label="🎯 Object Tracking"
      />
    </FormGroup>
    
    <Slider
      label="Detection Confidence"
      value={processingOptions.confidenceThreshold}
      min={0.3}
      max={0.9}
      step={0.05}
    />
    
    <Slider
      label="Blur Intensity"
      value={processingOptions.blurIntensity}
      min={15}
      max={75}
      step={5}
    />
  </AccordionDetails>
</Accordion>

// Display clips
{result.original_clip_url && (
  <div>
    <h4>Original Clip</h4>
    <video src={result.original_clip_url} controls />
  </div>
)}

{result.processed_clip_url ? (
  <div>
    <h4>Processed Clip</h4>
    <video src={result.processed_clip_url} controls />
    <Chip label="🏷️ Object Labels" />
    <Chip label="🔒 PII Blurred" />
  </div>
) : (
  <Button
    onClick={() => processClip(result)}
    disabled={processingClips.has(`${result.video_id}_${result.segment_id}`)}
  >
    {processingClips.has(`${result.video_id}_${result.segment_id}`)
      ? 'Processing...'
      : 'Process with YOLOv8'}
  </Button>
)}
```

---

## ✅ Implementation Checklist

### **Backend**
- [ ] Verify `yolo_service.py` exists and works
- [ ] Verify `pii_service.py` exists and works
- [ ] Create `clip_service.py` for clip extraction
- [ ] Add `/search/extract-and-process` endpoint
- [ ] Update search response schema
- [ ] Add summary to search results
- [ ] Test clip extraction with FFmpeg
- [ ] Test YOLOv8 processing
- [ ] Test PII blurring
- [ ] Test S3 caching

### **Frontend**
- [ ] Add processing options UI
- [ ] Add "Process Clip" button
- [ ] Display original clip
- [ ] Display processed clip
- [ ] Show processing indicators
- [ ] Display AI summary
- [ ] Add processing stats display
- [ ] Test end-to-end flow

### **Testing**
- [ ] Test search with 10 results
- [ ] Test clip extraction
- [ ] Test YOLOv8 labeling
- [ ] Test face blurring
- [ ] Test license plate blurring
- [ ] Test object tracking
- [ ] Test S3 caching
- [ ] Test concurrent processing

---

## 🎯 Success Criteria

1. ✅ Search returns 10 results (configurable)
2. ✅ Each result shows original clip
3. ✅ Each result can be processed with YOLOv8
4. ✅ Processed clips show object labels
5. ✅ Processed clips blur faces
6. ✅ Processed clips blur license plates
7. ✅ Object tracking works (YOLOv8)
8. ✅ AI summary displayed
9. ✅ Clips cached in S3
10. ✅ Performance: < 10s per clip processing

---

**Next Steps:**
1. Verify existing services
2. Implement clip extraction endpoint
3. Update frontend UI
4. Test end-to-end

**Estimated Time:** 4-6 hours
**Priority:** HIGH (Core functionality)
