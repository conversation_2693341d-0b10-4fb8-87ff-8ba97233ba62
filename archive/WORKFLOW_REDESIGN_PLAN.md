# Video Processing Workflow - Redesign Plan

## 🎯 New Requirements

### **Sequential Workflow:**
```
┌─────────────────────────────────────────────────────────────┐
│                    VIDEO UPLOAD COMPLETE                    │
└─────────────────────────────────────────────────────────────┘
                            ↓
┌─────────────────────────────────────────────────────────────┐
│ Step 1: Dual Embedding Generation (PARALLEL)               │
│ ✅ Nova Premier embedding                                   │
│ ✅ Marengo embedding                                        │
│ ✅ Both models run simultaneously                           │
│ ✅ Both complete before next step                           │
└─────────────────────────────────────────────────────────────┘
                            ↓
                    WAIT FOR BOTH
                            ↓
┌─────────────────────────────────────────────────────────────┐
│ Step 2: Video Summary Generation (SEQUENTIAL)              │
│ ✅ Use Marengo embeddings                                   │
│ ✅ Cluster scenes using embeddings                          │
│ ✅ Generate safety report                                   │
│ ✅ Identify patterns                                        │
└─────────────────────────────────────────────────────────────┘
                            ↓
┌─────────────────────────────────────────────────────────────┐
│ Step 3: Object Detection (MOVED TO SEARCH-TIME)            │
│ ❌ NOT during upload                                        │
│ ✅ Only during semantic search                              │
│ ✅ Process relevant segments on-demand                      │
│ ✅ Apply PII blurring (faces, plates)                       │
└─────────────────────────────────────────────────────────────┘
```

---

## 📋 Detailed Changes Required

### **1. Dual Embedding Generation**

**Current:**
- Uses ONE model (Nova Premier OR Marengo)
- Selected during upload

**New:**
- Use BOTH models simultaneously
- Wait for both to complete
- Compare model performance

**Implementation:**
```python
# After upload completes
def trigger_dual_embeddings(video_id, s3_key):
    # Start both embedding jobs in parallel
    nova_task = start_embedding_generation.delay(video_id, s3_key, "nova-premier")
    marengo_task = start_embedding_generation.delay(video_id, s3_key, "marengo")
    
    # Create a coordinator task that waits for both
    wait_for_embeddings.delay(video_id, nova_task.id, marengo_task.id)
```

**Database Changes:**
```python
# Add fields for both models
video.nova_embedding_started_at
video.nova_embedding_completed_at
video.nova_embedding_arn

video.marengo_embedding_started_at
video.marengo_embedding_completed_at
video.marengo_embedding_arn
```

---

### **2. Summary Generation Using Marengo Embeddings**

**Current:**
- Uses Nova Premier for summary
- Runs in parallel with embeddings

**New:**
- Wait for Marengo embeddings to complete
- Use embeddings to cluster scenes
- Generate safety report from clusters

**Implementation:**
```python
@celery_app.task
def generate_summary_from_embeddings(video_id):
    # 1. Fetch Marengo embeddings from Weaviate
    embeddings = vector_service.get_embeddings(video_id, model="marengo")
    
    # 2. Cluster scenes using embeddings
    clusters = cluster_scenes(embeddings)
    
    # 3. Generate summary for each cluster
    summaries = []
    for cluster in clusters:
        summary = generate_cluster_summary(cluster)
        summaries.append(summary)
    
    # 4. Generate overall safety report
    safety_report = generate_safety_report(summaries)
    
    # 5. Store in database
    video.summary = safety_report
    video.summary_completed_at = datetime.utcnow()
```

**Scene Clustering:**
```python
def cluster_scenes(embeddings):
    """
    Cluster embeddings into scenes using similarity
    - Group similar consecutive embeddings
    - Identify scene boundaries
    - Return clusters with timestamps
    """
    from sklearn.cluster import DBSCAN
    
    # Cluster embeddings
    clustering = DBSCAN(eps=0.3, min_samples=5)
    labels = clustering.fit_predict(embeddings)
    
    # Group by cluster
    clusters = {}
    for idx, label in enumerate(labels):
        if label not in clusters:
            clusters[label] = []
        clusters[label].append({
            'timestamp': embeddings[idx]['timestamp'],
            'embedding': embeddings[idx]['embedding']
        })
    
    return clusters
```

---

### **3. Object Detection Moved to Search-Time**

**Current:**
- Runs during upload
- Processes entire video
- Stores results in database

**New:**
- Only runs during semantic search
- Processes relevant segments
- Applies PII blurring on-demand

**Implementation:**
```python
@router.post("/search")
async def search_videos(request: SearchRequest):
    # 1. Semantic search for relevant segments
    results = vector_service.search(request.query, top_k=10)
    
    # 2. For each result, process with YOLO + PII
    processed_results = []
    for result in results:
        # Extract video segment
        segment = extract_segment(result.video_id, result.start_time, result.end_time)
        
        # Run object detection
        objects = detect_objects(segment)
        
        # Apply PII blurring
        blurred_segment = blur_pii(segment, objects)
        
        processed_results.append({
            'video_id': result.video_id,
            'segment': blurred_segment,
            'objects': objects,
            'timestamp': result.timestamp
        })
    
    return processed_results
```

**PII Blurring:**
```python
def blur_pii(video_segment, detected_objects):
    """
    Blur faces and license plates in video segment
    """
    for obj in detected_objects:
        if obj['class'] in ['person', 'face']:
            # Blur face region
            blur_region(video_segment, obj['bbox'])
        
        if obj['class'] == 'license_plate':
            # Blur license plate
            blur_region(video_segment, obj['bbox'])
    
    return video_segment
```

---

### **4. Celery Service Consolidation**

**Current:**
```yaml
services:
  celery-worker-fast:    # 8 workers
  celery-worker-slow:    # 16 workers
  celery-worker:         # 4 workers
  celery-beat:           # 1 worker
```

**New:**
```yaml
services:
  celery-worker:         # 20 workers (configurable)
  celery-beat:           # 1 worker
```

**Docker Compose Changes:**
```yaml
celery-worker:
  build:
    context: ./backend
    dockerfile: Dockerfile
  container_name: samsara-celery-worker
  command: celery -A app.core.celery_app worker --loglevel=info --concurrency=20 --hostname=worker@%h
  environment:
    - DATABASE_URL=***************************************************/samsara_db
    - REDIS_URL=redis://redis:6379/0
  volumes:
    - ./backend:/app
  depends_on:
    - redis
    - postgres
  networks:
    - samsara-network
```

**Remove Queue Routing:**
```python
# Remove from celery_app.py
task_routes = {}  # No more fast/slow queues
```

---

### **5. Timestamp Tracking**

**All tasks must have start/end timestamps:**

```python
# Embedding tasks
video.nova_embedding_started_at = datetime.utcnow()
video.nova_embedding_completed_at = datetime.utcnow()

video.marengo_embedding_started_at = datetime.utcnow()
video.marengo_embedding_completed_at = datetime.utcnow()

# Summary task
video.summary_started_at = datetime.utcnow()
video.summary_completed_at = datetime.utcnow()

# Object detection (search-time only)
# No database timestamps needed
```

---

## 🔄 New Workflow Implementation

### **Step 1: Upload Complete**
```python
@router.post("/upload")
async def upload_video(file: UploadFile):
    # 1. Upload to S3
    # 2. Extract metadata
    # 3. Create database record
    
    # 4. Trigger dual embedding generation
    trigger_dual_embeddings.delay(video_id, s3_key)
```

### **Step 2: Dual Embedding Coordinator**
```python
@celery_app.task
def trigger_dual_embeddings(video_id, s3_key):
    # Start both embedding jobs
    nova_result = start_embedding_generation.apply_async(
        args=[video_id, s3_key, "nova-premier"]
    )
    
    marengo_result = start_embedding_generation.apply_async(
        args=[video_id, s3_key, "marengo"]
    )
    
    # Wait for both to complete using Celery chord
    from celery import chord
    
    chord([nova_result, marengo_result])(
        on_embeddings_complete.s(video_id)
    )
```

### **Step 3: Summary After Embeddings**
```python
@celery_app.task
def on_embeddings_complete(results, video_id):
    """Called when both embeddings are complete"""
    logger.info(f"Both embeddings complete for {video_id}")
    
    # Trigger summary generation using Marengo embeddings
    generate_summary_from_embeddings.delay(video_id)
```

### **Step 4: Search-Time Object Detection**
```python
@router.post("/search")
async def search_videos(request: SearchRequest):
    # 1. Semantic search
    results = vector_service.search(request.query)
    
    # 2. Process each result with YOLO + PII
    processed = []
    for result in results:
        # Extract segment
        segment_path = extract_segment(result.video_id, result.timestamp)
        
        # Detect objects
        objects = yolo_service.detect_objects(segment_path)
        
        # Blur PII
        blurred_path = blur_pii(segment_path, objects)
        
        processed.append({
            'video_id': result.video_id,
            'segment_url': upload_to_s3(blurred_path),
            'objects': objects
        })
    
    return processed
```

---

## 📊 Database Schema Changes

### **New Fields Needed:**

```python
class Video(Base):
    # ... existing fields ...
    
    # Nova Premier embedding
    nova_embedding_started_at = Column(DateTime(timezone=True))
    nova_embedding_completed_at = Column(DateTime(timezone=True))
    nova_embedding_arn = Column(String)
    nova_indexed_at = Column(DateTime(timezone=True))
    
    # Marengo embedding
    marengo_embedding_started_at = Column(DateTime(timezone=True))
    marengo_embedding_completed_at = Column(DateTime(timezone=True))
    marengo_embedding_arn = Column(String)
    marengo_indexed_at = Column(DateTime(timezone=True))
    
    # Remove single embedding_model field
    # embedding_model = Column(SQLEnum(EmbeddingModel))  # DELETE
```

### **Migration Required:**
```bash
alembic revision --autogenerate -m "add_dual_embedding_fields"
alembic upgrade head
```

---

## 🎯 Implementation Checklist

### **Phase 1: Dual Embeddings**
- [ ] Add database fields for both models
- [ ] Create migration
- [ ] Implement dual embedding coordinator
- [ ] Update Weaviate schema to support both models
- [ ] Test parallel embedding generation

### **Phase 2: Sequential Summary**
- [ ] Implement embedding-based scene clustering
- [ ] Create summary generation from clusters
- [ ] Update to use Marengo embeddings
- [ ] Add dependency on embedding completion
- [ ] Test sequential flow

### **Phase 3: Search-Time Object Detection**
- [ ] Remove object detection from upload flow
- [ ] Implement segment extraction
- [ ] Add YOLO processing to search endpoint
- [ ] Implement PII blurring (faces, plates)
- [ ] Test search-time processing

### **Phase 4: Celery Consolidation**
- [ ] Update docker-compose.yml
- [ ] Remove queue routing
- [ ] Increase worker count to 20
- [ ] Remove fast/slow worker services
- [ ] Test with consolidated workers

### **Phase 5: Timestamp Tracking**
- [ ] Add timestamps to all tasks
- [ ] Update UI to show all timestamps
- [ ] Test timestamp accuracy

---

## ⚠️ Breaking Changes

1. **Database schema change** - requires migration
2. **Celery service consolidation** - requires docker-compose changes
3. **Object detection removed from upload** - changes user expectations
4. **Sequential processing** - slower overall completion time
5. **Dual embeddings** - 2x storage and processing cost

---

## 📈 Performance Impact

### **Before:**
```
Upload → [Embed, Summary, Detection] in parallel
Total time: ~5 minutes (longest task)
```

### **After:**
```
Upload → Embed (both) → Summary → (Detection at search-time)
Total time: ~10 minutes (sequential)
```

**Trade-off:**
- Slower upload processing
- Better summary quality (uses embeddings)
- Faster search (pre-computed embeddings)
- On-demand PII blurring

---

## 🚀 Estimated Effort

| Task | Effort | Priority |
|------|--------|----------|
| Dual embeddings | 4 hours | High |
| Sequential summary | 3 hours | High |
| Search-time detection | 5 hours | Medium |
| Celery consolidation | 2 hours | Low |
| Timestamp tracking | 2 hours | Medium |
| **Total** | **16 hours** | **2 days** |

---

## ✅ Next Steps

1. **Approve design** - Review and confirm requirements
2. **Create migration** - Add database fields
3. **Implement dual embeddings** - Core functionality
4. **Update summary generation** - Use embeddings
5. **Move object detection** - To search endpoint
6. **Consolidate Celery** - Single service
7. **Test end-to-end** - Verify workflow

**Ready to proceed with implementation?**
