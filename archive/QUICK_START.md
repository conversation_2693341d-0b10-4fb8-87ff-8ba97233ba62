# Quick Start Guide

## What Has Been Created

### ✅ Complete Backend (FastAPI)
- All API routes implemented (auth, videos, search, analytics, jobs, websocket)
- PostgreSQL models and schemas
- Weaviate vector database integration
- Celery async workers
- AWS services (S3, Bedrock)
- YOLOv8 video processing

### ✅ Frontend Structure (React + TypeScript)
- Project setup with TypeScript
- Service layer (API, auth, videos, search, websocket)
- Type definitions
- Basic routing structure
- TailwindCSS configuration

### ✅ Infrastructure (Docker Compose)
- 7 services ready to run
- PostgreSQL, Weaviate, Redis
- Backend, Celery workers
- Frontend container

## Start the Application

### Step 1: Start Infrastructure Services

```bash
cd /Users/<USER>/repos/samsara-poc-main

# Start database services
docker-compose up -d postgres redis weaviate

# Wait 10 seconds for services to be ready
sleep 10

# Verify services are healthy
docker-compose ps
```

Expected output:
```
NAME                  STATUS
samsara-postgres      Up (healthy)
samsara-redis         Up (healthy)
samsara-weaviate      Up (healthy)
```

### Step 2: Start Backend

```bash
# Start backend and workers
docker-compose up -d backend celery-worker celery-beat

# Check logs
docker-compose logs -f backend
```

Wait for: `✅ Application startup complete`

### Step 3: Test Backend API

```bash
# Health check
curl http://localhost:8000/health

# Login
curl -X POST http://localhost:8000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"password": "minfy2025"}'

# View API docs
open http://localhost:8000/docs
```

### Step 4: Start Frontend

```bash
# Install dependencies (first time only)
docker-compose run frontend npm install

# Start frontend
docker-compose up -d frontend

# Check logs
docker-compose logs -f frontend
```

Wait for: `Compiled successfully!`

### Step 5: Access Application

- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8000
- **API Docs**: http://localhost:8000/docs
- **Weaviate**: http://localhost:8080

## Test the Workflow

### 1. Login
- Password: `minfy2025`

### 2. Upload a Video
```bash
# Using API directly
TOKEN="your_token_from_login"

curl -X POST http://localhost:8000/api/videos/upload \
  -H "Authorization: Bearer $TOKEN" \
  -F "file=@/path/to/video.mp4" \
  -F "embedding_model=nova-premier"
```

### 3. Check Job Status
```bash
# List all jobs
curl http://localhost:8000/api/jobs \
  -H "Authorization: Bearer $TOKEN"

# Get specific video jobs
curl http://localhost:8000/api/videos/{video_id} \
  -H "Authorization: Bearer $TOKEN"
```

### 4. Search Videos (once indexed)
```bash
curl -X POST http://localhost:8000/api/search \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "driver distracted",
    "top_k": 5,
    "model": "nova-premier"
  }'
```

## Troubleshooting

### Backend won't start
```bash
# Check logs
docker-compose logs backend

# Rebuild
docker-compose build backend
docker-compose up -d backend
```

### Database connection error
```bash
# Check PostgreSQL
docker-compose exec postgres psql -U samsara -d samsara_db -c "SELECT 1;"

# Restart if needed
docker-compose restart postgres
docker-compose restart backend
```

### Celery tasks not running
```bash
# Check worker logs
docker-compose logs celery-worker

# Check Redis
docker-compose exec redis redis-cli ping

# Restart worker
docker-compose restart celery-worker
```

### Frontend errors
```bash
# Reinstall dependencies
docker-compose run frontend npm install

# Clear cache
docker-compose run frontend npm cache clean --force
docker-compose run frontend npm install

# Restart
docker-compose restart frontend
```

### Weaviate connection error
```bash
# Check Weaviate health
curl http://localhost:8080/v1/.well-known/ready

# Restart if needed
docker-compose restart weaviate
```

## Development Workflow

### Backend Development
```bash
# Enter backend container
docker-compose exec backend bash

# Run Python shell
python

# Test services
from app.services.aws_service import aws_service
from app.services.vector_service import vector_service
```

### Frontend Development
```bash
# Enter frontend container
docker-compose exec frontend sh

# Add new package
npm install package-name

# Run tests
npm test
```

### Database Management
```bash
# Connect to PostgreSQL
docker-compose exec postgres psql -U samsara -d samsara_db

# Common queries
SELECT * FROM videos;
SELECT * FROM processing_jobs WHERE status = 'running';
SELECT * FROM search_ratings ORDER BY created_at DESC LIMIT 10;
```

### View Logs
```bash
# All services
docker-compose logs -f

# Specific service
docker-compose logs -f backend
docker-compose logs -f celery-worker
docker-compose logs -f frontend

# Last 100 lines
docker-compose logs --tail=100 backend
```

## Next Steps

### Complete Frontend Implementation

The frontend structure is ready. Next steps:

1. **Implement Login Page** (`src/pages/LoginPage.tsx`)
   - Form with password input
   - Call authService.login()
   - Redirect to dashboard

2. **Implement Upload Page** (`src/pages/UploadPage.tsx`)
   - Drag-drop file upload
   - Model selection
   - Progress bar
   - WebSocket for real-time updates

3. **Implement Search Page** (`src/pages/SearchPage.tsx`)
   - Search bar with presets
   - Results grid
   - Video player
   - Rating system

4. **Implement Videos List** (`src/pages/VideosPage.tsx`)
   - Video cards with thumbnails
   - Status indicators
   - Filter and sort
   - Delete functionality

5. **Implement Analytics** (`src/pages/AnalyticsPage.tsx`)
   - Charts with Recharts
   - Model comparison
   - Statistics cards

### Testing Checklist

- [ ] Can login with password
- [ ] Can upload video
- [ ] Video processing starts
- [ ] Embeddings generated
- [ ] Can search videos
- [ ] Search returns results
- [ ] Can rate results
- [ ] Can view analytics
- [ ] WebSocket updates work
- [ ] Can delete videos

## Useful Commands

```bash
# Stop all services
docker-compose down

# Stop and remove volumes (clean slate)
docker-compose down -v

# Rebuild all services
docker-compose build

# View resource usage
docker stats

# Clean up Docker
docker system prune -a

# Export database
docker-compose exec postgres pg_dump -U samsara samsara_db > backup.sql

# Import database
docker-compose exec -T postgres psql -U samsara samsara_db < backup.sql
```

## Environment Variables

Current configuration (`.env`):
```
AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=jiTJCbKTcCROcAaIsDBqWOXuPxIN3qP4RQme2x1/
AWS_ACCOUNT_ID=************
S3_BUCKET=poc-video-search-bucket
BEDROCK_REGION=us-east-1
```

## API Endpoints Summary

### Authentication
- `POST /api/auth/login` - Login

### Videos
- `POST /api/videos/upload` - Upload video
- `GET /api/videos` - List videos
- `GET /api/videos/{id}` - Get video
- `DELETE /api/videos/{id}` - Delete video
- `POST /api/videos/{id}/index` - Start indexing
- `GET /api/videos/{id}/summary` - Get summary
- `GET /api/videos/{id}/stream` - Get stream URL

### Search
- `POST /api/search` - Search videos
- `POST /api/search/clip` - Extract clip
- `POST /api/search/process-clip` - Process clip
- `POST /api/search/rate` - Rate result
- `GET /api/search/presets` - Get presets

### Analytics
- `GET /api/analytics/ratings` - Rating stats
- `GET /api/analytics/models` - Model comparison
- `GET /api/analytics/videos` - Video stats
- `GET /api/analytics/dashboard` - Dashboard data

### Jobs
- `GET /api/jobs/{id}` - Get job status
- `GET /api/jobs/video/{id}` - Get video jobs
- `GET /api/jobs` - List all jobs
- `GET /api/jobs/stats/summary` - Job statistics

### WebSocket
- `WS /api/ws/{client_id}` - Real-time updates

## Support

For issues:
1. Check logs: `docker-compose logs [service]`
2. Check service health: `docker-compose ps`
3. Review documentation in `README_NEW.md`
4. Check implementation guide in `IMPLEMENTATION_GUIDE.md`

## Success!

Your application is now running with:
- ✅ FastAPI backend with all routes
- ✅ PostgreSQL database
- ✅ Weaviate vector database
- ✅ Redis cache
- ✅ Celery workers
- ✅ React frontend structure
- ✅ WebSocket support

Next: Implement the frontend pages and test the complete workflow!
